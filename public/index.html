<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="keywords" content="易宝开放平台,易宝,易宝支付,开放平台,支付接口,支付API,支付解决方案,微信支付,聚合支付" />
    <meta name="description" content="易宝开放平台——易宝开放平台(YOP, Yeepay Open Platform)旨在开放安全、快捷、可信赖的服务，助力开发者成长。在这里，你可以快速集成支付、鉴权、通知等各类能力及行业解决方案。" />
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 浏览器缓存 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta name="viewport"  content="width=device-width">
    <!-- 启用360浏览器的极速模式(webkit) -->
    <meta name="renderer" content="webkit">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <% if(htmlWebpackPlugin.options.env == 'production') { %>
        <script src="https://img.yeepay.com/yp-website-hosting/product/customizedresources/yop/js/probe-open.js" async="async"></script>
    <% } %>
    <script src="https://img.yeepay.com/fe-resources/yop-docs/js/webfunnySdk.js"></script>
    <script src="https://img.yeepay.com/fe-resources/yop-docs/js/ie.js"></script>
    <script>
        window.localStorage.wmUserInfo = JSON.stringify({ userTag: '未知', projectVersion: '1.0' })
    </script>
    <script>
      document.write('<script src="https://img.yeepay.com/fe-resources/feedback.js?'+new Date().getTime()+'"><\/script>');
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!--browser detection for >= ie9-->
    <script src="https://img.yeepay.com/yp-website-hosting/product/customizedresources/yop/js/browser-support-detector.js" type="text/javascript"></script>
    <!-- built files will be auto injected -->
    <script type="text/javascript">
      // 百度统计的代码换成：
			var _hmt = _hmt || [];
			(function() {
				var hm = document.createElement("script");
				hm.src = "https://hm.baidu.com/hm.js?cc01a8363ba506760456776e50793b7e";
				var s = document.getElementsByTagName("script")[0];
				s.parentNode.insertBefore(hm, s);
			})();
    </script>
  </body>
</html>

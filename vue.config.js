const path = require('path')
const PrerenderSPAPlugin = require('prerender-spa-plugin')
const Renderer = PrerenderSPAPlugin.PuppeteerRenderer
const replace = require('replace-in-file')

const staticPath = '/docs/static'
// 线上绝对路径
// const staticPath = 'https://img.yeepay.com/yp-website-hosting/product/yop-docs'
function ReplacePathInHTMLPlugin(cb) {
  this.apply = compiler => {
    if (compiler.hooks && compiler.hooks.done) {
      compiler.hooks.done.tap('replace-url', cb)
    }
  }
}

function replacePluginCallback() {
  replace({
    files: [
      path.join(__dirname, '/dist/*.html'),
      path.join(__dirname, '/dist/home/<USER>'),
      path.join(__dirname, '/dist//mhome/*.html'),
      path.join(__dirname, '/dist/solution/*/*.html'),
      path.join(__dirname, '/dist/js/*.js')
    ],
    from: [/\/productionReplacePath/g],
    to: staticPath,
  })
    .then(results => {
      console.log('replace HTML static resources success', results)
    })
    .catch(e => {
      console.log('replace HTML static resources fail', e)
    })
  }
function resolve(dir) {
  return path.join(__dirname, dir)
}
const routes = [
  '/home',
  '/mhome',
  '/solution/bankAndFund',
  '/solution/consumptionFinancial',
  '/solution/insurance',
  '/solution/electricity',
  '/solution/retail',
  '/solution/administrative',
  '/solution/airTravel',
  '/solution/crossBorder'
]
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/productionReplacePath' : '/',
  productionSourceMap: false,
  chainWebpack: config => {
    config.resolve.alias.set('@', resolve('src'))
    // ie报错无效字符 添加该配置项 解决该问题
    config.module
      .rule('view-design')
      .test(/view-design.src.*?js$/)
      .use('babel')
      .loader('babel-loader')
      .end()
    config.plugin('html').tap(args => {
      args[0].title = '易宝开放平台'
      args[0].env = process.env.NODE_ENV
      return args
    })
  },
  configureWebpack: {
    optimization: {
      splitChunks: {
        maxSize: 200000
      }
    },
    plugins:
      process.env.NODE_ENV === 'production'
        ? [
            new PrerenderSPAPlugin({
              // Required - The path to the webpack-outputted app to prerender.
              staticDir: path.join(__dirname, 'dist'),
              // Required - Routes to render.
              routes: routes,
              renderer: new Renderer({
                injectProperty: '__PRERENDER_INJECTED',
                inject: {
                  pre: true
                },
                headless: true,
                renderAfterDocumentEvent: 'render-event'
              }),
              server: {
                port: 9515,
                proxy: {
                  '/productionReplacePath': {
                    target: 'http://127.0.0.1:9515/',
                    pathRewrite: {
                      '^/productionReplacePath': ''
                    }
                  }
                }
              }
            }),
            new ReplacePathInHTMLPlugin(replacePluginCallback)
          ]
        : []
  },
  devServer: {
    proxy: {
      '^/apis': {
        target: 'https://qaopen.yeepay.com',
        changeOrigin: true,
        ws: false
      }
    }
  },
  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme

          'primary-color': '#52BF63',
          'link-color': '#52BF63',
          'border-radius-base': '2px'
        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true
      }
    }
  }
}

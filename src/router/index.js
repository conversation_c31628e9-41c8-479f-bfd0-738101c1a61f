import Vue from 'vue'
import VueRouter from 'vue-router'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
Vue.use(VueRouter)
const Product = () => import('@/views/Product')
const ApiDocs = () => import('@/views/ApiDocs')
const Guide = () => import('@/views/Guide')
const Open = () => import('@/views/Open')
const Problem = () => import('@/views/Problem')
const Home = () => import('@/views/PrerenderPage/Home.vue')
const Mhome = () => import('@/views/PrerenderPage/Mhome.vue')
const Search = () => import('@/views/Search')
const CustomSolution = () => import('@/views/CustomSolution')
const BankAndFund = () =>
  import('@/views/PrerenderPage/solution/BankAndFund.vue')
const ConsumptionFinancial = () =>
  import('@/views/PrerenderPage/solution/ConsumptionFinancial.vue')
const Insurance = () => import('@/views/PrerenderPage/solution/Insurance.vue')
const Electricity = () =>
  import('@/views/PrerenderPage/solution/Electricity.vue')
const Retail = () => import('@/views/PrerenderPage/solution/Retail.vue')
const Administrative = () =>
  import('@/views/PrerenderPage/solution/Administrative.vue')
const AirTravel = () => import('@/views/PrerenderPage/solution/AirTravel.vue')
const CrossBorder = () =>
  import('@/views/PrerenderPage/solution/CrossBorder.vue')
const routes = [
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '易宝开放平台'
    },
    alias: '/'
  },
  {
    path: '/mhome',
    name: 'Mhome',
    component: Mhome,
    meta: {
      enableFixed: true,
      maxWidth: 'none',
      title: '易宝开放平台'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: {
      maxWidth: '1280px'
    }
  },
  {
    path: '/solution/bankAndFund',
    name: 'BankAndFund',
    component: BankAndFund,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '银行与基金行业-支付解决方案'
    }
  },
  {
    path: '/solution/consumptionFinancial',
    name: 'ConsumptionFinancial',
    component: ConsumptionFinancial,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '消费金融-授权扣款-支付解决方案'
    }
  },
  {
    path: '/solution/insurance',
    name: 'Insurance',
    component: Insurance,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '保险行业-支付解决方案'
    }
  },
  {
    path: '/solution/electricity',
    name: 'Electricity',
    component: Electricity,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '电力行业-支付解决方案'
    }
  },
  {
    path: '/solution/retail',
    name: 'Retail',
    component: Retail,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '零售行业-支付解决方案'
    }
  },
  {
    path: '/solution/administrative',
    name: 'Administrative',
    component: Administrative,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '政务行业-支付解决方案'
    }
  },
  {
    path: '/solution/airTravel',
    name: 'AirTravel',
    component: AirTravel,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '航空旅游行业-支付解决方案'
    }
  },
  {
    path: '/solution/crossBorder',
    name: 'CrossBorder',
    component: CrossBorder,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '跨境行业-支付解决方案'
    }
  },
  {
    path: '/docs/*/products/*/apis',
    name: 'products',
    component: Product
  },
  {
    path: '/docs/solutions/*',
    name: 'solutions',
    component: CustomSolution
  },
  {
    path: '/docs/solutions/*/apis/*',
    name: 'solutions',
    component: CustomSolution
  },
  {
    path: '/docs/*/apis/*',
    name: 'docs',
    component: ApiDocs
  },
  {
    path: '/docs/*/products/*',
    name: 'products',
    component: Product
  },
  {
    path: '/docs/products/*',
    name: 'products',
    component: Product
  },
  {
    path: '/docs/*/open/*',
    name: 'open',
    component: Open
  },
  {
    path: '/docs/open/*',
    name: 'open',
    component: Open
  },
  {
    path: '/docs/apis/*',
    name: 'docs',
    component: ApiDocs
  },
  {
    path: '/docs',
    component: ApiDocs
  },
  {
    path: '/docs/platform/problem',
    name: 'problem',
    component: Problem
  },
  {
    path: '/docs/platform*',
    name: 'guide',
    component: Guide
  },
  {
    path: '/docs/*/platform/*',
    name: 'guide',
    component: Guide
  },
  {
    path: '/docs/403',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/403'),
    meta: { title: '403', maxWidth: '1280px' }
  },

  {
    path: '/docs/404',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/404'),
    meta: { title: '404', maxWidth: '1280px' }
  },
  {
    path: '/docs/500',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/500'),
    meta: { title: '500', maxWidth: '1280px' }
  },
  {
    path: '*',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/404'),
    meta: { title: '404', maxWidth: '1280px' }
  }
]

const router = new VueRouter({
  mode: 'history',
  fallback: false,
  routes
})

export default router

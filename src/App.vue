<template>
  <a-config-provider :locale="locale">
    <div id="app" class="global-wrap">
      <PageHeader />
      <a-spin :spinning="loading">
        <div slot="indicator">
          <img
            v-if="isMobile"
            src="@/assets/images/loading_32.gif"
            class="img-mobile-loading"
            alt
          />
          <img
            v-else
            src="@/assets/images/loading_64.gif"
            class="img-pc-loading"
            alt
          />
        </div>
        <div class="global-content">
           <router-view />
        </div>
        <div
          class="right-go-top-wrapper"
          :style="{ right: fixIconPosition, bottom: fixIconPosition }"
        >
          <a-popover placement="left" overlayClassName="right-go-top">
            <template slot="content">{{ $t('home.evaluate') }}</template>
            <div
              v-if="!/\/m{0,1}home/gi.test($route.path)"
              class="fix-right-icon"
              @click="showAdvice()"
            >
              <svg
                t="1610527901465"
                class="icon"
                style="width: 15px"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="3688"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <path
                  d="M910.037578 1016.167182H114.102124a106.056461 106.056461 0 0 1-106.056461-106.056461V114.102124C8.045663 55.368787 55.588214 8.045663 114.102124 8.045663h547.251339a31.451226 31.451226 0 0 1 31.451226 31.451226v16.676464a31.451226 31.451226 0 0 1-31.451226 31.524369H114.102124c-14.628477 0-26.550686 11.922209-26.550687 26.550686v795.935455a26.623829 26.623829 0 0 0 26.696971 26.696971h795.935455a26.623829 26.623829 0 0 0 26.696971-26.696971V332.066437c0-17.919885 14.482193-32.328935 32.328935-32.328935h14.921047c17.919885 0 32.328935 14.40905 32.328935 32.328935v577.971141a106.349031 106.349031 0 0 1-106.422173 106.056461z"
                  p-id="3689"
                />
                <path
                  d="M744.808926 671.227685a32.694647 32.694647 0 0 1 32.621505 32.621505v14.262765c0 18.06617-14.628477 32.621505-32.621505 32.621505H279.477061a32.621505 32.621505 0 0 1-32.621505-32.621505v-14.262765c0-18.06617 14.628477-32.621505 32.621505-32.621505h465.331865z"
                  p-id="3691"
                />
                <path
                  d="M744.808926 758.779122H279.477061a40.594025 40.594025 0 0 1-40.44774-40.520882v-14.262766c0-22.308428 18.212454-40.44774 40.44774-40.44774h465.331865c10.825073 0 20.991865 4.169116 28.744958 11.922209a40.374598 40.374598 0 0 1 11.922209 28.671816v14.262766a40.813452 40.813452 0 0 1-40.667167 40.374597z m-465.331865-79.725202a24.868412 24.868412 0 0 0-24.79527 24.79527v14.262765a24.868412 24.868412 0 0 0 24.868412 24.868412h465.258723a24.868412 24.868412 0 0 0 24.795269-24.868412v-14.262765a24.722127 24.722127 0 0 0-24.795269-24.79527H279.477061zM279.842772 326.434473a32.914074 32.914074 0 0 1-32.987216-32.914074v-13.531342a32.914074 32.914074 0 0 1 32.914074-32.914074H479.082634a32.914074 32.914074 0 0 1 32.987217 32.914074v13.458199a32.914074 32.914074 0 0 1-32.914074 32.914075H279.76963z"
                  p-id="3692"
                />
                <path
                  d="M279.842772 334.260708a40.813452 40.813452 0 0 1-40.813451-40.813452v-13.458199c0-22.67414 18.285597-40.813452 40.813451-40.813452H479.082634c22.67414 0 40.813452 18.285597 40.813452 40.813452v13.458199a40.813452 40.813452 0 0 1-40.813452 40.813452H279.842772z m0-15.65247H479.082634a25.234123 25.234123 0 0 0 25.160982-25.307266v-13.4582a25.234123 25.234123 0 0 0-25.307266-25.160981H279.623345a25.234123 25.234123 0 0 0-25.087838 25.307266v13.458199a25.380408 25.380408 0 0 0 25.307265 25.160982zM777.503573 491.955695v13.531341a33.279786 33.279786 0 0 1-33.133501 33.133501H279.842772a33.279786 33.279786 0 0 1-33.133501-33.133501v-13.531341a33.279786 33.279786 0 0 1 33.133501-33.133502h464.454158a33.133501 33.133501 0 0 1 33.133501 33.133502z"
                  p-id="3693"
                />
                <path
                  d="M744.516357 546.37363H279.842772a40.74031 40.74031 0 0 1-40.813451-40.813451v-13.604484c0-10.825073 4.388543-21.284435 11.922209-28.818101a41.398591 41.398591 0 0 1 28.891242-11.922209h464.454158c10.678788 0 21.211292 4.388543 28.891242 11.922209a41.032879 41.032879 0 0 1 11.922209 28.818101v13.531341c0 10.971358-4.242258 21.211292-11.922209 28.891243a40.520882 40.520882 0 0 1-28.671815 12.068494z m-464.673585-15.65247h464.454158a25.234123 25.234123 0 0 0 25.087838-25.087839v-13.677626a25.160981 25.160981 0 0 0-25.087838-25.087839h-464.454158a25.234123 25.234123 0 0 0-25.087838 25.234123v13.531342a25.599835 25.599835 0 0 0 7.314238 17.919885 25.453551 25.453551 0 0 0 17.7736 7.167954zM766.751642 287.010726a40.594025 40.594025 0 0 1-57.416773-57.270489l210.650074-209.91865a40.594025 40.594025 0 0 1 57.416774 57.051062l-210.650075 210.138077z"
                  p-id="3694"
                />
                <path
                  d="M738.006684 306.832313a48.493403 48.493403 0 0 1-34.376922-82.577755L914.279837 14.335908A48.71283 48.71283 0 0 1 948.510474 0h0.146285a48.493403 48.493403 0 0 1 34.376921 82.577755l-210.650074 210.138078a48.566545 48.566545 0 0 1-34.376922 14.11648zM948.656759 15.871898a33.060359 33.060359 0 0 0-23.186137 9.50851L714.966832 235.445344a32.767789 32.767789 0 0 0 46.445416 46.29913l210.650074-209.91865a32.621505 32.621505 0 0 0-23.405563-55.880784z"
                  p-id="3695"
                />
              </svg>
            </div>
          </a-popover>
          <a-popover
            placement="left"
            overlayClassName="right-go-top"
            v-if="showToTopBtn"
          >
            <template slot="content">{{ $t('home.top') }}</template>
            <a-back-top
              v-if="showToTopBtn"
              class="fix-right-icon"
              style="right: 0;bottom: 0"
            >
              <div class="to-top">
                <svg
                  t="1609317520439"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  style="width: 15px"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="4051"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <path
                    d="M557.63103 981.675139c-6.463863 59.006746-88.95811 53.758858-92.158041 0L465.40899 340.280769l-283.833968 273.914179c-44.479055 38.399184-97.469929-22.911513-64.382632-63.166657 125.757328-121.917409 351.352534-340.024774 356.472425-345.080667a49.918939 49.918939 0 0 1 75.774389 0c41.40712 40.063149 348.664591 336.376852 358.008393 347.000626 31.167338 38.719177-19.199592 96.061959-62.206678 63.806644-11.135763-9.471799-287.481891-276.474125-287.481891-276.474125l-0.127998 641.39437zM116.488405 3.327929h786.479287v89.278103H116.360407V3.327929z"
                    p-id="4052"
                  />
                </svg>
              </div>
            </a-back-top>
          </a-popover>
        </div>
      </a-spin>
       <low-code-renderer v-if="schema.components" :schema="schema" />
    </div>
  </a-config-provider>
</template>
<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import enUS from 'ant-design-vue/lib/locale-provider/en_US'
import PageHeader from '@/components/PageHeader'
import { BackTop } from 'ant-design-vue'
import { fetchSchema } from '@/utils/lowcode'
export default {
  components: {
    PageHeader,
    [BackTop.name]: BackTop
  },
  data() {
    return {
      locale: zhCN,
      schema: {}
    }
  },
  computed: {
    loading() {
      return this.$store.state.loading
    },
    showToTopBtn() {
      return this.$store.state.showToTopBtn
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    },
    customUserId() {
      return this.$store.state.customUserId
    },
    fixIconPosition() {
      if (this.isMobile) return '10px'
      return 32 + this.scrollBarWidth + 'px'
    },
    isMobile() {
      return this.$store.state.isMobile
    }
  },
  watch: {
    '$store.state.langType'() {
      const langType = window.localStorage.getItem('langType')
      if (langType && langType === 'en_US') {
        this.locale = enUS
        this.$i18n.locale = 'en'
      } else {
        this.locale = zhCN
        this.$i18n.locale = 'cn'
      }
      this.$store.dispatch('search/getCategory')
    },
    $route(newValue, oldValue) {
      if (newValue.path !== oldValue.path) {
        this.setTile()
        this.addPoint()
      }
    }
  },
  created() {
    this.toggleMobile()
    const that = this
    window.addEventListener('resize', () => {
      that.toggleMobile()
    })
    document.addEventListener('scroll', () => {
      that.onScroll()
    })
    this.setLang()
    this.$store.dispatch('search/getCategory')
    this.$store.commit('setCustomUserId')
  },
  async mounted() {
    this.scrollWidth()
    this.onScroll()
    this.setTile()
    this.addPoint()
    this.schema = await fetchSchema('feedback')
  },
  methods: {
    setTile() {
      const { title } = this.$route.meta
      if (!title) return
      document.title = title
    },
    addPoint() {
      try {
        const params = {
          userId: this.customUserId,
          pagePath: window.location.pathname,
          pageName: window.document.title,
          terminal: this.isMobile ? 'h5' : 'pc'
        }
        window.webfunnyEvent[1].trackEvent(params)
      } catch(err) {
        console.log(err)
      }

    },
    setLang() {
      const langType = window.localStorage.getItem('langType')
      if (langType && langType === 'en_US') {
        this.locale = enUS
        this.$i18n.locale = 'en'
        this.$store.commit('setLangType', 'en')
      } else {
        this.locale = zhCN
        this.$i18n.locale = 'cn'
        this.$store.commit('setLangType', 'cn')
      }
    },
    onScroll() {
      const htmlDom = document.documentElement
      const top = htmlDom.scrollTop
      const left = htmlDom.scrollLeft
      this.$store.commit('setScrollTop', top)
      this.$store.commit('setScrollLeft', left)
      if (top < 64) {
        this.$store.commit('setTop', 64 - top)
      }
      if (top > 64) {
        this.$store.commit('setTop', 0)
      }
      if (top > 468) {
        this.$store.commit('showToTop', true)
      }
      if (top < 468) {
        this.$store.commit('showToTop', false)
      }
    },
    showAdvice() {
      window.open(
        'https://www.wjx.cn/vj/YeLx1Ec.aspx',
        '_blank',
        'width=1000,height=600,left=300,top=150'
      )
    },
    showService() {
      const url = window.location.origin + '/service'
      window.open(url, '_blank', 'width=800,height=560,left=300,top=150')
    },
    scrollWidth() {
      const scrollContainer = document.createElement('div')
      const scrollContent = document.createElement('div')
      scrollContainer.setAttribute(
        'style',
        'position:fixed;left:-100000px;z-index:-1;width:50px;height:50px;overflow:scroll;'
      )
      scrollContent.setAttribute('style', 'height:100px;')
      scrollContainer.appendChild(scrollContent)
      document.body.appendChild(scrollContainer)
      const scrollBarWidth =
        scrollContainer.offsetWidth - scrollContent.offsetWidth
      this.$store.commit('setScrollBarWidth', scrollBarWidth)
    },
    toggleMobile() {
      const width = document.documentElement.clientWidth
      this.$store.commit('setClientWidth', width)
      if (width > 768) {
        if (this.$route.path === '/mhome') {
          this.$router.replace('/home')
        }
        this.$store.commit('setIsMobile', false)
        this.$store.commit('setShowComponents', 'PcLayout')
      } else {
        if (this.$route.path === '/home') {
          this.$router.replace('/mhome')
        }
        this.$store.commit('setIsMobile', true)
        this.$store.commit('setShowComponents', 'MobileLayout')
      }
    }
  }
}
</script>
<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', SimSun, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>

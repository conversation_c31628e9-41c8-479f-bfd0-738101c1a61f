import platform from 'platform'
export default {
  findHistoryApi,
  addMenuTier,
  getOpenKeys,
  handlerApiList,
  handlerApiOverview,
  findProductItem,
  findChild,
  getCustomUserId,
  addMenuFaq,
  hanleAnchorList
}
function findHistoryApi(list, apiId) {
  let api = {}
  for (let i = 0; i < list.length; i++) {
    if (list[i].apiId === apiId) {
      api = list[i]
      break
    }
  }
  return api
}
function findProductItem(list, location) {
  let item = ''
  if (!location) return list[0]
  for (let i = 0; i < list.length; i++) {
    if (list[i].location === location) {
      item = list[i]
      break
    }
    if (list[i].children && list[i].children.length > 0 && !item) {
      item = findProductItem(list[i].children, location)
    }
  }
  return item
}
function findChild(list, location) {
  let item = ''
  if (!location) return list[0]
  for (let i = 0; i < list.length; i++) {
    let locationSplitArr = list[i].location.split('/')
    if (locationSplitArr[locationSplitArr.length -1] === location) {
      item = list[i]
      break
    }
    if (list[i].children && list[i].children.length > 0 && !item) {
      item = findChild(list[i].children, location)
    }
  }
  return item
}
function addMenuTier(list) {
  for (let i = 0; i < list.length; i++) {
    if (!list[i].children || list[i].children.length === 0) {
      list[i].maxTier = 1
    } else {
      list[i].maxTier = findTier(list[i].children, 1)
    }
  }
}
function findTier(list, num) {
  const item = list.find(item => item.children && item.children.length > 0)
  if (!item) {
    return num + 1
  }
  return findTier(item.children, num + 1)
}
function getOpenKeys(str) {
  const arr = []
  if (!str) return []
  const stringArr = str.split('/')
  if (stringArr.length < 2) return []
  for (let i = 1; i < stringArr.length; i++) {
    const key = stringArr.slice(1, i + 1).join('/')
    arr.push(key)
  }
  return arr
}

function handlerApiList(list) {
  if(!list || !Array.isArray(list)) return []
  const arr = []
  list.forEach(item => {
    const { name, items } = item
    if (items.length < 1) return
    items.forEach((subItem, index) => {
      arr.push({
        ...subItem,
        name,
        extend: {
          scenes:
            subItem.extend.scenes === '无\n' || subItem.extend.scenes === '无'
              ? ''
              : subItem.extend.scenes
        },
        rowSpan: index === 0 ? items.length : 0
      })
    })
  })
  return arr
}
function handlerApiOverview(list) {
  const arr = []
  list.forEach(item => {
    const { name, children: items } = item
    if (items.length < 1) return
    if (items.length === 1) {
      arr.push({
        location: item.location,
        title: name,
        name: '',
        desc: ''
      })
      return
    }
    items.forEach((subItem, index) => {
      arr.push({
        desc: subItem.desc,
        name: subItem.name,
        location: `${item.location}/${subItem.code}`,
        title: name,
        rowSpan: index === 0 ? items.length : 0
      })
    })
  })
  return arr
}

function getCustomUserId() {
  const info = platform.parse(window.navigator.userAgent)
  const id = `${info.name}-${info.version}-${info.os}-${new Date().getTime()}`
  return window.btoa(id)
}

function addMenuFaq(menu, docNo) {
  menu.push({
    contentType: 'JSON',
    contentUri: `/docs/faqs/doc/${docNo}/faqs.json`,
    hasContent: true,
    hasRef: false,
    location: 'faqs',
    templateId: 'DOC_FAQS',
    title: '常见问题',
  })
}

function hanleAnchorList(anchorList, pageFaqList) {
  const pageFaqListAnchor = []
  if(pageFaqList.length > 0) {
    pageFaqListAnchor.push(
      {
        id: '#faqListAnchor',
        title: '常见问题',
        children: pageFaqList.map(item => {
          return {
            id: `#${item.title}faq`,
            title: item.title,
            children: [] 
          }
        })
      }
    )
  }
  return [...anchorList, ...pageFaqListAnchor]
}
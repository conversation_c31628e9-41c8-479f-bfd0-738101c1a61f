import hljs from 'highlight.js'
function highlight(el, binding) {
  let block = el.querySelectorAll('pre code')[0]
  let lang = binding.value
  try {
    let str = block.innerHTML
    // 得到经过highlight.js之后的html代码
    if (lang && lang === 'PHP') {
      str = str.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    }
    str = str.replace(/\n$/g, '')
    const preCode = lang
      ? hljs.highlight(lang, str, true).value
      : hljs.highlightAuto(str).value
    // 以换行进行分割
    const linesLength = preCode.split(/\n/).length

    // 生成行号 aria-hidden 对浏览器语义化隐藏
    let linesNum = '<span aria-hidden="true" class="line-numbers-rows">'
    for (let index = 0; index < linesLength; index++) {
      linesNum = linesNum + '<span></span>'
    }
    linesNum += '</span>'

    let html = preCode
    html = '<pre class="hljs"><code>' + html + '</code>' + linesNum + '</pre>'

    el.innerHTML = html
  } catch (__) {
    // eslint-disable-next-line
    console.log('执行错误', __)
  }
}
export default {
  install(Vue) {
    Vue.directive('highlight', {
      bind: highlight,
      update: highlight
    })
  }
}

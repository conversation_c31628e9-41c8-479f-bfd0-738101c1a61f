let pageSize = 30
export default function(list) {
  let deepClonelist = JSON.parse(JSON.stringify(list))
  let listMap = {}
  deepClonelist.forEach(item => {
    if (item.items.length > pageSize) {
      listMap[item.code] = getPageList(item.items)
      item.items = getPageList(item.items)[0]
    }
  })
  return {
    list: deepClonelist,
    listMap
  }
}
function getPageList(data) {
  let filterData = []
  let length = Math.ceil(data.length / pageSize)
  for (let i = 0; i < length; i++) {
    if (i == length - 1) {
      filterData.push(data.slice(i * pageSize))
    } else {
      filterData.push(data.slice(i * pageSize, i * pageSize + pageSize))
    }
  }
  return filterData
}
export function getItems(list) {
  let arr = []
  let deepClonelist = JSON.parse(JSON.stringify(list))
  deepClonelist.forEach(item => {
    if (item.items && item.items.length > 0) {
      arr = arr.concat(handlerLoaction(item.items))
    }
    if (item.children && item.children.length > 0) {
      arr = arr.concat(getItems(item.children))
    }
  })
  return arr
}

function handlerLoaction(list) {
  return list.map(item => {
    const arr = item.location.split('/')
    return {
      ...item,
      docNo: arr[arr.length - 3],
      pageNo: arr[arr.length - 1],
      openKeys: getOpenKeys(arr)
    }
  })
}
function getOpenKeys(arr) {
  const openKeys = []
  let latesLocation = arr[0]
  for (let index = 1; index < arr.length - 1; index++) {
    const element = arr[index]
    latesLocation = `${latesLocation}/${element}`
    openKeys.push(latesLocation)
  }
  return openKeys
}

export function filterList(list) {
  return list.filter(item => {
    if (item.children) {
      item.children = filterList(item.children)
    }
    return item.children.length > 0 || item.items.length > 0
  })
}

export function handleRespParams(respParams) {
  const resHeader = {}
  const resBody = {}
  Object.keys(respParams).forEach(key => {
    resHeader[key] = []
    resBody[key] = []
    const list = respParams[key]
    list.forEach(item => {
      if (item.in === 'header') {
        resHeader[key].push(item)
      } else {
        resBody[key].push(item)
      }
    })
  })
  return {
    resHeader,
    resBody
  }
}

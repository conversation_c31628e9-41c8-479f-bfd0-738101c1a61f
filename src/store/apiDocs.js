import api from '@/api'
import faq from '@/api/faq'
import { getItems, filterList, handleRespParams } from '@/utils/initMenu'
export default {
  namespaced: true,
  state: {
    menuCode: '',
    keyWord: '',
    currentApiId: '',
    subMenuApiId: '',
    detailLoading: false,
    menuList: [],
    itemsList: [],
    apiOverviewlist: [],
    menuMap: {},
    scenesMsg: '',
    invokingMsg: '',
    filterMenuList: [],
    definition: {},
    common: {},
    resHeader: {}, // 应该放在头里面的响应参数，要是下载接口
    errcode: [],
    callbackList: [],
    historyList: [],
    apiFaqList: [],
    spiApiMapData: {}, // spi api 关联数据
    apiBasicData: {}, // api 基本信息
    showSpiApiData: [], // spiapi 关联需要展示的数据
  },
  mutations: {
    setShowComponents(state, payload) {
      state.showComponents = payload
    },
    setMenuActive(state, payload) {
      state.menuCode = payload
    },
    setDefinition(state, payload) {
      state.definition = payload
    },
    setKeyWord(state, payload) {
      state.keyWord = payload
    },
    setSubMenuActive(state, payload) {
      // 匹配 页面编码
      let payloadArr = payload.split('/')
      let currentPage = payloadArr[payloadArr.length - 1]
      console.log(currentPage, '<--currentPage')
      if (!payload) {
        state.subMenuApiId = ''
        return
      }
      // 点击闪烁问题
      this.commit('apiDocs/setHistoryList', [])
      // const reg = new RegExp(`${payload}$`)
      // const reg = new RegExp(`${currentPage}$`)
      // 匹配包含 文档编码 页面编码 对应菜单
      console.log(payloadArr,'<--payloadArr')
      let reg = null
      let item = {}
      // 没有文档编码时 默认第一个
      if(payloadArr.length === 1) {
        reg = new RegExp(`${currentPage}`)
        item = state.itemsList.find(menu => reg.test(menu.location))
      } else {
        reg = new RegExp(`${payloadArr[0]}.*?${currentPage}$`)
        item = state.itemsList.find(menu => reg.test(menu.location))
      }
      if (!item) {
        state.subMenuApiId = ''
        state.currentApiId = ''
        return
      }
      state.subMenuApiId = item.location
      state.currentApiId = item.apiId
      this.commit('apiDocs/setMenuActive', item.openKeys)
      this.dispatch('apiDocs/getApiExtend', {
        docNo: item.docNo,
        pageNo: item.pageNo
      })
      console.log('itemitemitemitemitemitemitem')
      console.log(item)
      this.dispatch('apiDocs/init', {
        apiId: item.apiId,
        spiCount: item.spiCount
      })
      document.title = `易宝开放平台-${item.title}`
      // historyCount
      if (item.historyCount > 1) {
        // 当前版本是历史版本，根据父apiId拿历史
        if (item.latestRef) {
          this.dispatch('apiDocs/gethistoryList', item.latestRef)
          return
        }
        this.dispatch('apiDocs/gethistoryList', item.apiId)
      }
    },
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setShowSpiApiData(state, payload) {
      state.showSpiApiData = payload
    },
    setItemsList(state, payload) {
      state.itemsList = payload
    },
    setFilterMenuList(state, payload) {
      state.filterMenuList = payload
    },
    setFilterMenuItems(state, payload) {
      const items = state.filterMenuList.find(
        item => item.code === state.menuCode
      ).items
      items.push(...payload)
    },
    setHistoryList(state, payload) {
      state.historyList = payload
    }
  },
  actions: {
    init({ dispatch, state, rootState }, { apiId, spiCount }) {
      if (!rootState.loading) state.detailLoading = true
      const list = [
        // dispatch('getSpiApiMap', apiId),
        dispatch('getDefinition', apiId),
        dispatch('getErrcode', apiId),
        dispatch('getApiFaqList', apiId),
      ]
      console.log('spiCount')
      console.log(spiCount)
      if (spiCount > 0) {
        list.push(dispatch('getSpi', apiId))
      } else {
        state.callbackList = []
      }
      Promise.all(list).then(() => {
        state.detailLoading = false
      })
    },
    getMenu({ commit }, location) {
      commit('setLoading', true, { root: true })
      commit('setDefinition', {})
      api
        .getMenuTree()
        .then(res => {
          const list = res
          // 过滤空菜单
          const newList = filterList(list)
          commit('setMenuList', newList)
          commit('setFilterMenuList', newList)
          commit('setItemsList', getItems(newList))
          commit('setSubMenuActive', location)
        })
        .finally(() => {
          commit('setLoading', false, { root: true })
        })
    },
    getConent({ commit }, location) {
      commit('setDefinition', {})
      commit('setSubMenuActive', location)
    },
    gethistoryList({ commit }, apiId) {
      api
        .getHistory({
          apiId
        })
        .then(res => {
          commit('setHistoryList', res)
        })
    },
    getErrcode({ state }, apiId) {
      api
        .getErrcode({
          apiId
        })
        .then(res => {
          state.errcode = res
          state.pageNo = 1
        })
    },
    getDefinition({ state, dispatch }, apiId) {
      api
        .getDefinition({
          apiId
        })
        .then(res => {
          state.definition = res
          const { resHeader, resBody } = handleRespParams(res.respParams)
          state.definition.respParams = resBody
          state.resHeader = resHeader
          dispatch('getCommon', res.commonRef)
        })
    },
    // 获取spi api 关联数据
    getSpiApiMap({ state, commit }, { apiId, spi} ) {
      api.getSpiApiMap({
        apiId: apiId
      })
      .then(res => {
        state.spiApiMapData = res
        api.getApibasic()
          .then(resData => {
            state.apiBasicData = resData
            let showSpiApiData = []
            if(state.spiApiMapData[spi]) {
              for (let key in resData) {
                state.spiApiMapData[spi].forEach((item) => {
                  if(key === item) {
                    showSpiApiData.push(resData[key])
                  }
                })
              }
            }
            commit('setShowSpiApiData', showSpiApiData)
          })
        })
    },
    getSpi({ state, dispatch }, apiId) {
      state.callbackList = []
      api
        .getSpi({
          apiId
        })
        .then(res => {
          state.callbackList = res
          dispatch('getSpiApiMap', {
            apiId: apiId,
            spi: res[0].name
          })
        })
    },
    getApiExtend({ state }, params) {
      api
        .getApiExtend({
          docNo: params.docNo,
          pageNo: params.pageNo
        })
        .then(res => {
          state.scenesMsg = res[0] ? res[0].data : ''
          state.invokingMsg = res[1]
            ? `<div class="markdown-con">${res[1].data}</div>`
            : ''
        })
    },
    getCommon({ state }, refName) {
      api
        .getCommon({
          refName
        })
        .then(res => {
          state.common = res
        })
    },
    getApiFaqList({ state }, apiId) {
      state.apiFaqList = []
      faq.getApiFaqList({
          apiId
        })
        .then(res => {
          state.apiFaqList = res
        })
    }
  }
}

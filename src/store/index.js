import Vue from 'vue'
import Vuex from 'vuex'
import utils from '@/utils'
import apiDocs from './apiDocs'
import guide from './guide'
import product from './product'
import search from './search'
import open from './open'
import solutions from './solutions'

Vue.use(Vuex)
const origin = window.location.origin
export default new Vuex.Store({
  state: {
    commonMenu: [
      {
        title: 'menu.solution',
        key: '1',
        path: `${origin}/#/solution/main`,
      },
      {
        title: 'menu.product',
        key: '2',
        path: `${origin}/docs/v2/index.html`,
      },
      {
        title: 'menu.doc',
        key: '3',
        children: [
          {
            title: 'menu.guideTitle',
            key: '3-1',
            path: '/docs/platform',
          },
          {
            title: 'menu.apiDocsTitle',
            key: '3-2',
            path: '/docs',
          },
          {
            title: 'menu.questionTitle',
            key: '3-3',
            path: '/docs/platform/problem',
          },
        ],
      },
      {
        title: 'loginDeveloper',
        key: '4',
        path: 'https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html',
      },
    ],
    footerLinkList: [
      {
        title: 'menu.product',
        children: [
          {
            title: 'menu.zfcp',
            contentUri: '/docs/v2/products/yjzf/index.html',
          },
          {
            title: 'menu.zjgl',
            contentUri: '/docs/v2/products/recharge/index.html',
          },
          {
            title: 'menu.rzfw',
            contentUri: '/docs/v2/products/identify00001/index.html',
          },
        ],
      },
      {
        title: 'menu.solution',
        children: [
          {
            title: 'menu.retail',
            contentUri: '/solution/retail',
          },
          {
            title: 'menu.bankAndFund',
            contentUri: '/solution/bankAndFund',
          },
          {
            title: 'menu.consumptionFinancial',
            contentUri: '/solution/consumptionFinancial',
          },
          {
            title: 'menu.airTravel',
            contentUri: '/solution/airTravel',
          },
          {
            title: 'menu.administrative',
            contentUri: '/solution/administrative',
          },
          {
            title: 'menu.crossBorder',
            contentUri: '/solution/crossBorder',
          },
          {
            title: 'menu.electricity',
            contentUri: '/solution/electricity',
          },
          {
            title: 'menu.insurance',
            contentUri: '/solution/insurance',
          },
        ],
      },
      {
        title: 'menu.developer',
        children: [
          {
            title: 'menu.guideTitle',
            contentUri: '/docs/platform/user-guide',
          },
          {
            title: 'menu.apiDocsTitle',
            contentUri: '/docs',
          },
          {
            title: 'menu.developTools',
            contentUri: '/docs/platform/sdk_guide/sdk-guide',
          },
          {
            title: 'menu.help',
            contentUri: '/docs/platform/faq/faq',
          },
        ],
      },
      {
        title: 'menu.callUs',
        children: [
          {
            title: 'menu.platformDesc',
            contentUri: '/docs/v2/platform/platform-profile/index.html',
          },
          {
            title: 'menu.cooperation',
            contentUri:
              'https://www.yeepay.com/customerService/businessCooperation',
          },
          {
            title: 'menu.callUs',
            contentUri: '/docs/platform/faq/contact_us',
          },
          {
            title: 'menu.yeepayCom',
            contentUri: 'https://www.yeepay.com',
          },
        ],
      },
    ],
    solutionList: [
      {
        name: '通用行业',
        icon: '&#xe676;',
        iconSize: '12px',
        content: {
          list: [
            {
              title: '标准商户收付款解决方案',
              desc: '针对单一主体商户提供的，包括多场景收单和付款的综合解决方案。',
              linkUrl: '/docs/v2/products/bzshsfk/index.html',
            },
            {
              title: '平台商收付解决方案',
              desc: '针对平台类商户提供的收付款综合解决方案。',
              linkUrl: '/docs/v2/products/ptssfk/index.html',
            },
            {
              title: '服务商解决方案',
              desc: 'SaaS服务商提供的，包括支付、结算、分账、付款等服务的收付款综合解决方案。',
              linkUrl: '/docs/v2/products/fwssfk/index.html',
            },
          ],
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/tongyong.png',
        },
      },
      {
        name: '零售',
        icon: '&#xe671;',
        iconSize: '13px',
        content: {
          title: '零售行业',
          desc: '将全场景支付与账户体系相关联，通过融入全场景支付方式，并叠加经营属性的增值产品，深度赋能行业SAAS服务商。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/dalingshou.png',
          linkUrl: '/solution/retail',
        },
      },
      {
        name: '航旅',
        icon: '&#xe679;',
        iconSize: '12px',
        content: {
          title: '航旅行业',
          desc: '深度扎根航旅行业，提供权威的支付服务，并联合金融机构提供金融增值服务解决方案。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/hanglv.png',
          iconUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/hanglv-icon.png',
          linkUrl: '/solution/airTravel',
        },
      },
      {
        name: '银基',
        icon: '&#xe674;',
        iconSize: '10px',
        content: {
          title: '银行与金融行业',
          desc: '聚焦互联网金融持牌业务（如银行、基金），开拓和深耕银行、基金业客户。顺应形势发展，不断进行产品创新，为银行和基金客户，提供优质服务。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/yinji.png',
          iconUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/yinji-icon.png',
          linkUrl: '/solution/bankAndFund',
        },
      },
      {
        name: '金融',
        icon: '&#xe678;',
        iconSize: '15px',
        content: {
          title: '消费金融',
          desc: '专注于链接金融生态的消费端、资金端及消费场景，输出行业支付解决方案和增值服务。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/jinrong.png',
          iconUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/jinrong-icon.png',
          linkUrl: '/solution/consumptionFinancial',
        },
      },
      {
        name: '跨境',
        icon: '&#xe67a;',
        iconSize: '15px',
        content: {
          title: '跨境行业',
          desc: '引领合规的跨境支付平台，通过支付链接中国与世界。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/kuajing.png',
          linkUrl: '/solution/crossBorder',
        },
      },
      {
        name: '政务',
        icon: '&#xe672;',
        iconSize: '14px',
        content: {
          title: '政务行业',
          desc: '易宝支付政务行业线致力于服务于政务各相关行业，提供综合支付服务，同时为省级财政非税收入提供缴费服务。',
          imageUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/zhengwu.png',
          iconUrl:
            'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/zhengwu-icon.png',
          linkUrl: '/solution/administrative',
        },
      },
      // {
      //   name: '电力',
      //   icon: '&#xe673;',
      //   iconSize: '16px',
      //   content: {
      //     title: '电力',
      //     desc:
      //       '易宝支付电力行业线致力于深耕电力行业，构建电力产业生态圈，向水、气、热等能源行业拓展。以深度开展支付业务为基础，根据商户需求定制行业解决方案，并联合金融机构为商户提供增值服务。',
      //     imageUrl: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/dianli.png',
      //     iconUrl: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/dianli-icon.png',
      //     linkUrl: '/docs/v2/products/yjf/index.html'
      //   }
      // },
      // {
      //   name: '保险',
      //   icon: '&#xe677;',
      //   iconSize: '16px',
      //   content: {
      //     title: '保险',
      //     desc:
      //       '2011年成立以来，专注于行业客户资金的归结、结算、管理，主力发展保险电子商户，优化保险业渠道结构，培训新的业务增长点。',
      //     imageUrl: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/baoxian.png',
      //     iconUrl: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/baoxian-icon.png',
      //     linkUrl: '/docs/v2/products/ymf/index.html'
      //   }
      // }
    ],
    recommendAppList: [
      {
        name: '支付产品',
        img: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/1.png',
        content: {
          title: '支付产品',
          subTitle: '支持业内所有主流支付方式，满足用户多样化的支付需求。',
          iconList: [
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/4.png',
              text: '一键支付',
              contentUri: '/docs/v2/products/yjzf/index.html',
            },
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/3.png',
              text: '网银支付',
              contentUri: '/docs/v2/products/e-bank/index.html',
            },
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/2.png',
              text: '微信公众号支付',
              contentUri: '/docs/v2/products/wechat-offiaccount/index.html',
            },
          ],
        },
      },
      {
        name: '资金管理',
        img: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/2.png',
        content: {
          title: '资金管理',
          subTitle: '为商家提供安全、便捷的资金管理服务。',
          iconList: [
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/1.png',
              text: '线下汇入',
              contentUri: '/docs/v2/products/recharge/index.html',
            },
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/3.png',
              text: '公对公转账',
              contentUri: '/docs/v2/products/ptzz/index.html',
            },
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/4.png',
              text: '代付代发',
              contentUri: '/docs/v2/products/dfdf/index.html',
            },
          ],
        },
      },
      {
        name: '认证服务',
        img: 'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/1.png',
        content: {
          title: '认证服务',
          subTitle: '支持个人身份、银行卡信息的真实性、准确性、一致性的验证。',
          iconList: [
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/3.png',
              text: '银行卡认证',
              contentUri: '/docs/v2/products/identify00001/index.html',
            },
            {
              imgUrl:
                'https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/2.png',
              text: '实名认证',
              contentUri: '/docs/v2/products/identify00001/index.html',
            },
          ],
        },
      },
    ],
    mobileMenuMap: {},
    showToTopBtn: false,
    loading: false,
    mobileLeftShow: false,
    subMenu: false,
    showComponents: 'PcLayout',
    langType: 'cn',
    isMobile: false,
    leftActive: '',
    rightActive: '',
    rootCode: '',
    customUserId: '',
    scrollBarWidth: 15,
    clientWidth: 15,
    scrollTop: 0,
    scrollLeft: 0,
    lock: 0,
    top: 64,
  },
  mutations: {
    setClientWidth(state, payload) {
      state.clientWidth = payload
    },
    setScrollLeft(state, payload) {
      state.scrollLeft = payload
    },
    setScrollTop(state, payload) {
      state.scrollTop = payload
    },
    setIsMobile(state, payload) {
      state.isMobile = payload
    },
    setLangType(state, payload) {
      state.langType = payload
    },
    setSubMenu(state, payload) {
      state.subMenu = payload
    },
    setLoading(state, payload) {
      state.loading = payload
    },
    setLock(state, payload) {
      state.lock = payload
    },
    setLeftActive(state, payload) {
      state.leftActive = payload
    },
    setRightActive(state, payload) {
      state.rightActive = payload
    },
    setTop(state, payload) {
      state.top = payload
    },
    setRootCode(state, payload) {
      state.rootCode = payload
    },
    toggleMobileLeftShow(state) {
      state.mobileLeftShow = !state.mobileLeftShow
    },
    setMobileLeftShow(state, payload) {
      state.mobileLeftShow = payload
    },
    setShowComponents(state, payload) {
      state.showComponents = payload
    },
    setScrollBarWidth(state, payload) {
      state.scrollBarWidth = payload
    },
    showToTop(state, payload) {
      state.showToTopBtn = payload
    },
    setCustomUserId(state) {
      const customUserId = window.localStorage.getItem('customUserId')
      if(customUserId) {
        state.customUserId = customUserId
      } else {
        const id = utils.getCustomUserId()
        state.customUserId = id
        window.localStorage.setItem('customUserId', id)
      }
    }
  },
  actions: {
    toggleLock({ commit }, value) {
      if (value) {
        commit('setLock', this.lock + 1)
        document.querySelector('html').className = 'body-lock'
      } else {
        if (this.lock > 0) {
          commit('setLock', this.lock - 1)
          return
        }
        document.querySelector('html').className = ''
      }
    },
  },
  getters: {},
  modules: {
    apiDocs,
    product,
    search,
    guide,
    open,
    solutions
  },
})

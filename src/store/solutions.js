import api from '@/api'
import solutions from '@/api/solutions'
import faq from '@/api/faq'
import utils from '@/utils'
import { handleRespParams } from '@/utils/initMenu'
export default {
  namespaced: true,
  state: {
    menuList: [],
    currentLocation: '',
    upCurrentLocation: [],
    pageFaqList: [],
    contentType: 'MD', //JSON、MD、LINK, 区分渲染逻辑
    templateId: '', //区分渲染模板,API列表页、SPI列表页
    docInfo: {},
    loading: '',
    detailLoading: false,
    content: '',
    docNo: '',
    html: '',
    menuCode: '',
    keyWord: '',
    currentApiId: '',
    subMenuApiId: '',
    itemsList: [],
    apiOverviewlist: [],
    menuMap: {},
    scenesMsg: '',
    invokingMsg: '',
    filterMenuList: [],
    definition: {},
    common: {},
    resHeader: {}, // 应该放在头里面的响应参数，要是下载接口
    errcode: [],
    callbackList: [],
    historyList: [],
    apiFaqList: []
  },
  mutations: {
    setContentType(state, payload) {
      state.contentType = payload
    },
    setTemplateId(state, payload) {
      state.templateId = payload
    },
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setUpCurrentLocation(state, payload) {
      state.upCurrentLocation = payload
    },
    setDocNo(state, payload) {
      state.docNo = payload
    },
    setCurrentLocation(state, payload) {
      state.currentLocation = payload
    },
    setDocInfo(state, payload) {
      state.docInfo = payload
    },
    setHtml(state, payload) {
      state.html = payload
    },
    setDetailLoading(state, payload) {
      state.detailLoading = payload
    },
    setContent(state, payload) {
      state.content = payload
    },
    setPageFaqList(state, payload) {
      state.pageFaqList = payload
    },
    setDefinition(state, payload) {
      state.definition = payload
    },
    setKeyWord(state, payload) {
      state.keyWord = payload
    },
    setSubMenuActive(state, payload) {
      
      // 匹配 页面编码
      let payloadArr = location.pathname.split('/')
      let currentPage = payloadArr[payloadArr.length - 1]
      if (!payload) {
        state.subMenuApiId = ''
        return
      }
      // 点击闪烁问题
      this.commit('solutions/setHistoryList', [])
      // const reg = new RegExp(`${currentPage}$`)
      // 跳转链接url 匹配正则
      // const item = state.itemsList.find(menu => reg.test(menu.location))
      const item = state.itemsList.find(menu => {
        // return menu.location === payload
        return menu.uri === currentPage
      })
      if (!item) {
        state.subMenuApiId = ''
        state.currentApiId = ''
        return
      }
      state.subMenuApiId = payload
      state.currentApiId = item.apiId
      this.commit('solutions/setMenuActive', item.openKeys)
      this.dispatch('solutions/getApiExtend', {
        docNo: state.docNo,
        pageNo: item.uri
      })
      this.dispatch('solutions/init', {
        apiId: item.apiId,
        spiCount: item.spiCount
      })
      document.title = `易宝开放平台-${state.docInfo.docTitle}`
      // historyCount
      if (item.historyCount > 1) {
        // 当前版本是历史版本，根据父apiId拿历史
        if (item.latestRef) {
          this.dispatch('solutions/gethistoryList', item.latestRef)
          return
        }
        this.dispatch('solutions/gethistoryList', item.apiId)
      }
    },
    setMenuActive(state, payload) {
      state.menuCode = payload
    },
    setItemsList(state, payload) {
      state.itemsList = payload
    },
    setHistoryList(state, payload) {
      state.historyList = payload
    }

  },
  actions: {
    init({ dispatch, state, rootState }, { apiId, spiCount }) {
      if (!rootState.loading) state.detailLoading = true
      const list = [
        dispatch('getDefinition', apiId),
        dispatch('getErrcode', apiId),
        dispatch('getApiFaqList', apiId)
      ]
      console.log(spiCount, '<--spiCount')
      list.push(dispatch('getSpi', apiId))
      Promise.all(list).then(() => {
        state.detailLoading = false
      })
    },
    async getMenu({ commit, dispatch }, { location, docNo }) {
      try {
        commit('setLoading', true, { root: true })
        commit('setDocNo', docNo)
        const res = await solutions.getInfo(docNo)
        const { docVersion, docTitle } = res.data
        document.title = `易宝开放平台-${docTitle}`
        commit('setDocInfo', res.data)
        const menu = await solutions.getMenu({ docNo, docVersion })
        const apiCategories = await solutions.getApiCategories({ docNo, docVersion })
        const faqList = await faq.getDocFaqList({ docNo })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, docNo)
        }
        // const apiObj = menu.find(item => item.appendChildren && item.templateId === 'SOLUTION_APIS')
        const apiObjIndex = menu.findIndex(item => item.appendChildren && item.templateId === 'SOLUTION_APIS')
        let _apiCategories = []
        if (apiCategories.length && apiObjIndex >= 0) {
          console.log(apiCategories[0].items)
          if (apiCategories.length === 1) {
            _apiCategories = apiCategories[0].items.map(child => {
              return {
                ...child,
                location: 'apis/' + child.location.substring(child.location.indexOf('/') + 1)
              }
            })
          } else {
            _apiCategories = apiCategories.map(item => {
              return {
                ...item,
                title: item.name,
                location: 'apis/' + item.code,
                children: item.items.map(child => {
                  return {
                    ...child,
                    location: 'apis/' + child.location.substring(child.location.indexOf('/') + 1)
                  }
                })
              }
            })
          }
          console.log(_apiCategories)
          menu[apiObjIndex] = {
            ...menu[apiObjIndex],
            children: _apiCategories
          }
          console.log(menu)

        }

        commit('setDefinition', {})


        commit('setMenuList', menu)
        await dispatch('getContent')
        let newList = []


        if (apiCategories.length === 1) {
          newList = _apiCategories
        } else {
          _apiCategories.forEach(child => {
            newList.push(...child.children)
          })
        }
        console.log('newListnewListnewList')
        console.log(newList)
        commit('setItemsList', newList)
        commit('setSubMenuActive', location)
        commit('setLoading', false, { root: true })
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    async getMenu2({ commit, dispatch }, { docNo, location }) {
      try {
        commit('setLoading', true, { root: true })
        commit('setDocNo', docNo)
        const res = await solutions.getInfo(docNo)
        const { docVersion, docTitle } = res.data
        document.title = `易宝开放平台-${docTitle}`
        commit('setDocInfo', res.data)
        const menu = await solutions.getMenu({ docNo, docVersion })
        const faqList = await faq.getDocFaqList({ docNo })
        const apiCategories = await solutions.getApiCategories({ docNo, docVersion })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, docNo)
        }

        const apiObjIndex = menu.findIndex(item => item.appendChildren && item.templateId === 'SOLUTION_APIS')
        let _apiCategories = []

        if (apiCategories.length && apiObjIndex >= 0) {
          if (apiCategories.length === 1) {
            _apiCategories = apiCategories[0].items.map(child => {
              // let locationArr = child.location.split('/')
              return {
                ...child,
                location: 'apis/' + child.location.substring(child.location.indexOf('/') + 1)
                // location: 'apis/' + locationArr[locationArr.length-1]
              }
            })
          } else {
            _apiCategories = apiCategories.map(item => {
              // let locationArr = child.location.split('/')
              return {
                ...item,
                title: item.name,
                location: 'apis/' + item.code,
                children: item.items.map(child => {
                  return {
                    ...child,
                    location: 'apis/' + child.location.substring(child.location.indexOf('/') + 1)
                  }
                })
              }
            })
          }

          menu[apiObjIndex] = {
            ...menu[apiObjIndex],
            children: _apiCategories
          }
          console.log(menu)
        }

        commit('setMenuList', menu)
        commit('setLoading', false, { root: true })
        dispatch('getContent', location)
        let newList = []
        if (apiCategories.length === 1) {
          newList = _apiCategories
        } else {
          _apiCategories.forEach(child => {
            newList.push(...child.children)
          })
        }
        commit('setItemsList', newList)
        commit('setSubMenuActive', location)
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    getContent({ commit, dispatch, state }, paramsUri) {
      // 匹配 页面编码
      let item = null
      let payloadArr = paramsUri ? paramsUri.split('/') : []
      let currentPage = payloadArr[payloadArr.length - 1]
      if(paramsUri) {
        item = utils.findChild(state.menuList, currentPage)
      } else {
        item = utils.findChild(state.menuList, '')
      }
      // item = utils.findProductItem(state.menuList, paramsUri)
      try {
        // 链接最后页面标识的不带全情况下  根据目录从前往后找第一个页面
        let current = state.menuList.find((item) => item.pageNo === currentPage)
        if (current) {
          if (currentPage === current.pageNo){
            if (!item.hasContent){
              item = item.children ? item.children[0] : item
            }
          }
        }
        
      } catch (error) {
        console.log(error)
      }
      const { contentUri, contentType, templateId, location, pageId } = item
      commit('setContentType', contentType)
      commit('setTemplateId', templateId)
      commit('setCurrentLocation', location)
      commit('setDefinition', {})
      commit('setSubMenuActive', location)
      if (contentType === 'JSON') {
        return dispatch('getJson', contentUri)
      } else {
        return dispatch('getHtml', { url: contentUri, pageId } )
      }
    },
    getApiExtend({ state }, params) {
      api
        .getApiExtend({
          docNo: params.docNo,
          pageNo: params.pageNo
        })
        .then(res => {
          state.scenesMsg = res[0] ? res[0].data : ''
          state.invokingMsg = res[1]
            ? `<div class="markdown-con">${res[1].data}</div>`
            : ''
        })
    },
    gethistoryList({ commit }, apiId) {
      api
        .getHistory({
          apiId
        })
        .then(res => {
          commit('setHistoryList', res)
        })
    },
    getHtml({ commit }, { url, pageId}) {
      if (!url) return
      faq.getPageFaqList({ pageId })
        .then(res => {
          commit('setPageFaqList', res)
        })
      return solutions.getHtml(url).then(res => {
        commit('setHtml', res)
      })
    },
    getJson({ commit }, url) {
      if (!url) return
      return solutions.getJson(url).then(res => {
        commit('setContent', res)
      })
    },
    getCommon({ state }, refName) {
      api
        .getCommon({
          refName
        })
        .then(res => {
          state.common = res
        })
    },
    getErrcode({ state }, apiId) {
      api
        .getErrcode({
          apiId
        })
        .then(res => {
          state.errcode = res
          state.pageNo = 1
        })
    },
    getSpi({ state }, apiId) {
      state.callbackList = []
      api
        .getSpi({
          apiId
        })
        .then(res => {
          state.callbackList = res || []
        })
    },
    getDefinition({ state, dispatch }, apiId) {
      api
        .getDefinition({
          apiId
        })
        .then(res => {
          state.definition = res
          const { resHeader, resBody } = handleRespParams(res.respParams)
          state.definition.respParams = resBody
          state.resHeader = resHeader
          dispatch('getCommon', res.commonRef)
        })
    },
    getApiFaqList({ state }, apiId) {
      state.apiFaqList = []
      faq.getApiFaqList({
        apiId
      })
        .then(res => {
          state.apiFaqList = res
        })
    }
  }
}

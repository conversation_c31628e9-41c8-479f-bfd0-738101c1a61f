import apiGuide from '@/api/guide'
import faq from '@/api/faq'
import utils from '@/utils'
export default {
  namespaced: true,
  state: {
    menuList: [],
    currentLocation: '',
    upCurrentLocation: '',
    loading: '',
    html: '',
    currentTitle: '',
    docVersion: '',
    content: '',
    pageFaqList: [],
    templateId: ''
  },
  mutations: {
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setCurrentTitle(state, payload) {
      state.currentTitle = payload
    },
    setHtml(state, payload) {
      state.html = payload
    },
    setContent(state, payload) {
      state.content = payload
    },
    setPageFaqList(state, payload) {
      state.pageFaqList = payload
    },
    setTemplateId(state, payload) {
      state.templateId = payload
    },
    setDocVersion(state, payload) {
      state.docVersion = payload
    },
    setUpCurrentLocation(state, payload) {
      state.upCurrentLocation = payload
    },
    setCurrentLocation(state, payload) {
      state.currentLocation = payload
      let currentItem = {}
      if (
        state.upCurrentLocation &&
        state.upCurrentLocation !== state.currentLocation
      ) {
        const item = state.menuList.find(
          item => item.location === state.upCurrentLocation
        )
        currentItem = item.children.find(
          item => item.location === state.currentLocation
        )
        document.title = `易宝开放平台-${currentItem.title}`
      } else {
        currentItem = state.menuList.find(
          item => item.location === state.currentLocation
        )
        document.title = `易宝开放平台-${currentItem.title}`
      }
      const { contentUri, contentType, templateId, pageId } = currentItem 
      this.commit('guide/setTemplateId', templateId)
      if (contentType === 'JSON') {
        return this.dispatch('guide/getJson', contentUri)
      } else {
        return this.dispatch('guide/getHtml', { url: contentUri, pageId } )
      }
    }
  },
  actions: {
    async getMenu({ commit }, location) {
      try {
        const res = await apiGuide.getInfo()
        const { docVersion, docTitle } = res.data
        document.title = `易宝开放平台-${docTitle}`
        commit('setDocVersion', docVersion)
        const menu = await apiGuide.getMenu({ docVersion })
        const faqList = await faq.getDocFaqList({ docNo: 'platform' })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, 'platform')
        }
        commit('setMenuList', menu)
        const item = menu[0]
        if (location) {
          commit('setCurrentLocation', location)
        } else {
          commit('setUpCurrentLocation', item.location)
          if (!item.children || item.children.length === 0) {
            commit('setCurrentLocation', item.location)
          } else {
            commit('setCurrentLocation', item.children[0].location)
          }
        }
      } catch (error) {
        // eslint-disable-next-line
        console.log(error)
        commit('setMenuList', [])
      }
    },
    async getContent({ commit, state }, location) {
      try {
        commit('setLoading', true, { root: true })
        const menu = state.menuList
        const item = menu[0]
        if (location) {
          commit('setCurrentLocation', location)
        } else {
          commit('setUpCurrentLocation', item.location)
          if (!item.children || item.children.length === 0) {
            commit('setCurrentLocation', item.location)
          } else {
            commit('setCurrentLocation', item.children[0].location)
          }
        }
        commit('setLoading', false, { root: true })
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    getHtml({ commit }, { url, pageId}) {
      if (!url) return
      faq.getPageFaqList({ pageId })
      .then(res => {
        commit('setPageFaqList', res)
      })
      return apiGuide.getHtml(url).then(res => {
        commit('setHtml', res)
      })
    },
    getJson({ commit }, url) {
      if (!url) return
      return apiGuide.getJson(url).then(res => {
        commit('setContent', res)
      })
    }
  }
}

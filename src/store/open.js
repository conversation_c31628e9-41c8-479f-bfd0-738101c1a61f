import open from '@/api/open'
import faq from '@/api/faq'
import utils from '@/utils'
export default {
  namespaced: true,
  state: {
    menuList: [],
    currentLocation: '',
    upCurrentLocation: [],
    pageFaqList: [],
    contentType: 'MD', //JSO<PERSON>、MD、LINK, 区分渲染逻辑
    templateId: '', //区分渲染模板,API列表页、SPI列表页
    docInfo: {},
    loading: '',
    detailLoading: false,
    content: '',
    docNo: '',
    html: ''
  },
  mutations: {
    setContentType(state, payload) {
      state.contentType = payload
    },
    setTemplateId(state, payload) {
      state.templateId = payload
    },
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setUpCurrentLocation(state, payload) {
      state.upCurrentLocation = payload
    },
    setDocNo(state, payload) {
      state.docNo = payload
    },
    setCurrentLocation(state, payload) {
      state.currentLocation = payload
    },
    setDocInfo(state, payload) {
      state.docInfo = payload
    },
    setHtml(state, payload) {
      state.html = payload
    },
    setDetailLoading(state, payload) {
      state.detailLoading = payload
    },
    setContent(state, payload) {
      state.content = payload
    },
    setPageFaqList(state, payload) {
      state.pageFaqList = payload
    }
  },
  actions: {
    async getMenu({ commit, dispatch }, docNo) {
      try {
        commit('setLoading', true, { root: true })
        commit('setDocNo', docNo)
        const res = await open.getInfo(docNo)
        const { docVersion, docTitle } = res.data
        document.title = `易宝开放平台-${docTitle}`
        commit('setDocInfo', res.data)
        const menu = await open.getMenu({ docNo, docVersion })
        const faqList = await faq.getDocFaqList({ docNo })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, docNo)
        }
        commit('setMenuList', menu)
        await dispatch('getContent')
        commit('setLoading', false, { root: true })
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    async getMenu2({ commit, dispatch }, { docNo, location }) {
      try {
        commit('setLoading', true, { root: true })
        commit('setDocNo', docNo)
        const res = await open.getInfo(docNo)
        const { docVersion } = res.data
        commit('setDocInfo', res.data)
        const menu = await open.getMenu({ docNo, docVersion })
        const faqList = await faq.getDocFaqList({ docNo })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, docNo)
        }
        commit('setMenuList', menu)
        commit('setLoading', false, { root: true })
        dispatch('getContent', location)
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    getContent({ commit, dispatch, state }, paramsUri) {
      const item = utils.findProductItem(state.menuList, paramsUri)
      const { contentUri, contentType, templateId, location, pageId } = item
      commit('setContentType', contentType)
      commit('setTemplateId', templateId)
      commit('setCurrentLocation', location)
      if (contentType === 'JSON') {
        return dispatch('getJson', contentUri)
      } else {
        return dispatch('getHtml', { url: contentUri, pageId } )
      }
    },
    getHtml({ commit }, { url, pageId}) {
      if (!url) return
      faq.getPageFaqList({ pageId })
      .then(res => {
        commit('setPageFaqList', res)
      })
      return open.getHtml(url).then(res => {
        commit('setHtml', res)
      })
    },
    getJson({ commit }, url) {
      if (!url) return
      return open.getJson(url).then(res => {
        commit('setContent', res)
      })
    }
  }
}

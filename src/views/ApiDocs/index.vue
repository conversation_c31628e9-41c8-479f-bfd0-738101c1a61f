<template>
  <div>
    <Component :is="showComponents" />
  </div>
</template>
<script>
import PcLayout from './PcLayout'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {}
  },
  computed: {
    showComponents() {
      return this.$store.state.showComponents
    }
  },
  watch: {
    $route(newvalue, oldvalue) {
      if(newvalue.path === oldvalue.path) return
      this.init2()
    },
    '$store.state.langType'() {
      this.init()
    }
  },
  mounted() {
    this.init()
    this.$upLog(30001)
  },
  methods: {
    init() {
      const { path } = this.$route
      let location = path.split('apis/')[1]
      if (location && location.indexOf('/index.html') !== -1) {
        location = location.replace('/index.html', '')
      }
      this.$store.dispatch('apiDocs/getMenu', location)
    },
    init2() {
      const { path } = this.$route
      document.documentElement.scrollTop = 0
      if (path === '/docs') {
        this.$store.commit('apiDocs/setSubMenuActive', '')
        this.$store.commit('apiDocs/setMenuActive', [])
        return
      }
      let location = path.split('apis/')[1]
      if (location && location.indexOf('/index.html') !== -1) {
        location = location.replace('/index.html', '')
      }
      this.$store.dispatch('apiDocs/getConent', location)
    }
  }
}
</script>

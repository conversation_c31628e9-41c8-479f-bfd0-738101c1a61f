<template>
  <div class="simple-wrap">
    <a-layout id="components-layout" class="pc">
      <div
        class="trigger-wrap"
        @click="() => (collapsed = !collapsed)"
        :style="{
          background: '#fff',
          position: 'fixed',
          zIndex: 1000,
          bottom: '50px',
          left: !collapsed ? '231px' : 0
        }"
      >
        <a-icon
          class="trigger"
          :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        />
      </div>
      <a-layout style="margin-top: 2px">
        <div
          id="pc-left-menu"
          :style="{
            width: `${scrollBarWidth + 230}px`,
            top: `${top + 1}px`
          }"
        >
          <a-layout-sider
            width="230"
            :style="{
              height: '100%',
              background: '#fff'
            }"
            v-show="!collapsed"
            :trigger="null"
            collapsible
          >
            <h3
              class="title"
              :style="{ 'margin-bottom': isShowSearch ? 0 : '16px' }"
            >
              {{ $t('menu.apiDocsTitle') }}
              <a-icon
                @click="toggleShowSearch"
                style="float: right; font-size: 18px; color: rgba(0, 0, 0, 0.45); margin-top: 5px; "
                type="search"
              />
            </h3>
            <div
              class="search-menu"
              v-if="isShowSearch"
              style="margin-bottom: 16px"
            >
              <Input
                clearable
                ref="searchInput"
                :value="keyWord"
                :maxLength="100"
                :placeholder="$t('keyWordSearch')"
                @on-change="onChange"
                @on-search="onChange"
              />
            </div>
            <template v-if="filterMenuList.length > 0">
              <a-menu
                :style="{
                  overflow: 'hidden',
                  borderRight: 0
                }"
                :inlineIndent="16"
                mode="inline"
                :open-keys="openKeys"
                :selectedKeys="subMenuApiId ? [subMenuApiId] : []"
                @openChange="onOpenChange"
                @click="onMenuClick"
              >
                <template v-for="(menu, menuIndex) in filterMenuList">
                  <a-menu-item-group
                    class="level-1"
                    :key="menu.location"
                    :id="`menu-${menuIndex}`"
                  >
                    <span slot="title">{{ menu.name }}</span>
                    <template v-for="subMenu in menu.children">
                      <ApiMenuItem
                        :menu-info="subMenu"
                        :key="subMenu.location"
                      />
                    </template>
                  </a-menu-item-group>
                </template>
              </a-menu>
            </template>
            <template v-else>
              <a-empty v-if="!loading" style="margin-top: 20px" />
            </template>
          </a-layout-sider>
        </div>
        <a-layout
          :style="{
            padding: '16px',
            display: 'block',
            marginLeft: collapsed ? '0' : '232px'
          }"
        >
          <div
            :style="{
              width: `calc(100vw - ${(collapsed ? 0 : 262) +
                scrollBarWidth}px)`,
              minHeight: 'calc(100vw - 64px)',
              background: '#fff',
              padding: '24px 0 24px 24px'
            }"
          >
            <Detail v-if="subMenuApiId" />
            <LazyComponent v-else :time="500">
              <ApiOverview />
            </LazyComponent>
          </div>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>
<script>
import mixin from '@/mixins'
import Detail from './Detail'
import { Input } from 'view-design'
export default {
  mixins: [mixin],
  components: {
    Input,
    Detail
  },
  data() {
    return {
      collapsed: false,
      busy: false,
      isShowSearch: false
    }
  },
  watch: {
    isShowSearch(newValue) {
      if (newValue) {
        this.$nextTick(() => {
          this.$refs.searchInput.focus()
        })
      }
    }
  },
  computed: {
    top() {
      return this.$store.state.top
    },
    loading() {
      return this.$store.state.loading
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    }
  },
  methods: {
    toggleShowSearch() {
      this.isShowSearch = !this.isShowSearch
      if (this.isShowSearch) {
        this.$upLog(30003)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.pc-menu {
  .api-path {
    word-break: break-all;
    white-space: pre-line;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .api-name {
    word-break: break-all;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 17px;
  }
}
.search-menu {
  padding: 20px 15px 0;
}
</style>

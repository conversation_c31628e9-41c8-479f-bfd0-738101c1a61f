<template>
  <div class="mobile">
    <MobileHeader :navName="$t('menu.apiDocsTitle')">
      <h3 class="mobile-h3-title">{{ $t('menu.apiDocsTitle') }}</h3>
      <div class="search">
        <a-input
          allow-clear
          :value="keyWord"
          :maxLength="100"
          :placeholder="$t('keyWordSearch')"
          @change="onChange"
          @search="onChange"
        >
          <a-icon type="search" slot="prefix" />
        </a-input>
      </div>
      <template v-if="filterMenuList.length > 0">
        <a-menu
          :style="{
            borderRight: 0
          }"
          :inlineIndent="16"
          mode="inline"
          :open-keys="openKeys"
          :selectedKeys="subMenuApiId ? [subMenuApiId] : []"
          @openChange="onOpenChange"
          @click="onMenuClick"
        >
          <template v-for="(menu, menuIndex) in filterMenuList">
            <a-menu-item-group
              class="level-1"
              :key="menu.location"
              :id="`menu-${menuIndex}`"
            >
              <span slot="title">{{ menu.name }}</span>
              <template v-for="subMenu in menu.children">
                <ApiMenuItem :menu-info="subMenu" :key="subMenu.location" />
              </template>
            </a-menu-item-group>
          </template>
        </a-menu>
      </template>
      <template v-else>
        <div style="text-align:center;width: 100%;">
          <a-empty style="margin-top: 20px" />
        </div>
      </template>
    </MobileHeader>
    <div class="mobile-content2" id="content" style="padding-top: 60px">
      <Detail v-if="subMenuApiId" />
      <ApiOverview v-else />
    </div>
  </div>
</template>

<script>
import MobileHeader from '@/components/MobileHeader'
import mixin from '@/mixins'
import Detail from './Detail'
import ApiOverview from './ApiOverview'
export default {
  mixins: [mixin],
  components: {
    Detail,
    ApiOverview,
    MobileHeader
  },
  data() {
    return {
      show: false,
      subMenu: false,
      data: [],
      loading: false,
      busy: false,
      currentPage: 1,
      menuIndex: 1
    }
  },
  watch: {
    subMenuList(newValue) {
      if (newValue) {
        this.currentPage = 1
        this.data = newValue.slice(0, 20)
      }
    },
    subMenu(newValue) {
      this.$store.dispatch('toggleLock', newValue)
    }
  },
  computed: {
    routerName() {
      return this.$route.path
    },
    commonMenu() {
      return this.$store.state.commonMenu
    },
    mobileLeftShow() {
      return this.$store.state.mobileLeftShow
    }
  },
  methods: {
    showSubMenu() {
      this.subMenu = !this.subMenu
      this.$store.commit('setMobileLeftShow', false)
    }
  }
}
</script>
<style lang="less" scoped></style>

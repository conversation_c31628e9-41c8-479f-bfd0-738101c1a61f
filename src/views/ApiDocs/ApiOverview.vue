<template>
  <div class="api-overview-wrap">
    <h1 class="api-overview">{{ $t('apiOverview') }}</h1>
    <template v-for="(level1, levelI) in list">
      <template v-for="(item, index) in level1.children">
        <div :key="index + '-' + levelI" style="margin-bottom: 16px">
          <h4 style="margin-bottom: 16px">
            {{ item.name }}
          </h4>
          <ApiOverviewTable :list="item.children" />
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import ApiOverviewTable from './ApiOverviewTable'
export default {
  components: {
    ApiOverviewTable
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile
    },
    list() {
      return this.$store.state.apiDocs.menuList
    }
  },
  created() {
    document.title = '易宝开放平台-API概览'
  }
}
</script>

<style lang="less" scoped>
.api-overview-wrap {
  min-height: calc(100vh - 64px);
  padding-right: 24px;
  .api-overview {
    border-bottom: 1px solid rgba(232, 232, 232, 1);
    padding-bottom: 12px;
    margin-bottom: 24px;
  }
}
</style>

<template>
  <a-spin :spinning="detailLoading">
    <div slot="indicator">
      <img
        v-if="isMobile"
        src="@/assets/images/loading_32.gif"
        class="img-mobile-loading"
        alt
      />
      <img
        v-else
        src="@/assets/images/loading_64.gif"
        class="img-pc-loading"
        alt
      />
    </div>
    <div class="Product-wrap">
      <template v-if="html || content">
        <ProductApi v-if="templateId === 'PRODUCT_APIS'" />
        <FaqDoc v-else-if="templateId === 'DOC_FAQS'" :faqList="docFaqList" />
        <template v-else>
          <a-row>
            <a-col :md="24" :lg="comAnchorList.length > 1 ? 20 : 24">
              <RenderMarkdown :html="html" @getAnchorList="getAnchorList" />
              <FapPage :faqList="pageFaqList" />
            </a-col>
            <a-col
              :md="0"
              :lg="comAnchorList.length > 1 ? 4 : 0"
              style="padding-left: 20px"
              v-if="!isMobile"
            >
              <AnchorList :anchorList="comAnchorList" />
            </a-col>
          </a-row>
        </template>
      </template>
      <div v-else-if="!loading" style="padding-top: 200px">
        <LazyComponent :time="500">
          <a-empty />
        </LazyComponent>
      </div>
    </div>
  </a-spin>
</template>
<script>
import RenderMarkdown from '@/components/RenderMarkdown'
import FapPage from '@/components/FapPage'
import FaqDoc from '@/components/FaqDoc'
import utils from '@/utils'
import ProductApi from './ProductApi'
export default {
  components: {
    RenderMarkdown,
    ProductApi,
    FaqDoc,
    FapPage
  },
  data() {
    return {
      anchorList: []
    }
  },
  computed: {
    html() {
      return this.$store.state.product.html
    },
    templateId() {
      return this.$store.state.product.templateId
    },
    content() {
      return this.$store.state.product.content
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    loading() {
      return this.$store.state.loading
    },
    detailLoading() {
      return this.$store.state.product.detailLoading
    },
    pageFaqList() {
      return this.$store.state.product.pageFaqList
    },
    docFaqList() {
      return this.content ? this.content.items : []
    },
    comAnchorList() {
      const { anchorList,  pageFaqList } = this
      return utils.hanleAnchorList(anchorList, pageFaqList)
    }
  },
  methods: {
    getAnchorList(list) {
      this.anchorList = list
    }
  }
}
</script>

<style lang="less" scoped>
.Product-wrap {
  min-height: calc(100vh - 64px);
}
</style>

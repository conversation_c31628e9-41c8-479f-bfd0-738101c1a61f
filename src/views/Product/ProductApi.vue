<template>
  <div class="ProductApi-wrap" style="padding-top: 24px;">
    <div class="header">
      <h1 class="header-title">{{ $t('apiList') }}</h1>
    </div>
    <a-table
      v-if="list2.length < 1"
      key="model"
      :columns="columns"
      rowKey="apiId"
      :data-source="list"
      bordered
      :pagination="false"
    >
      <a
        :href="`/docs/apis/${record.linkLocation}`"
        slot="apiName"
        slot-scope="text, record"
        >{{ text }}</a
      >
    </a-table>
    <a-table
      v-else
      key="noModel"
      :columns="columns2"
      rowKey="apiId"
      :data-source="list2"
      bordered
      :pagination="false"
    >
      <a
        :href="`/docs/apis/${record.linkLocation}`"
        slot="apiName"
        slot-scope="text, record"
        >{{ text }}</a
      >
    </a-table>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import utils from '@/utils'
export default {
  data() {
    return {
      columns: [
        {
          title: () => this.$t('productModal'),
          dataIndex: 'name',
          width: '220px',
          customRender: (value, row) => {
            return {
              children: value,
              attrs: {
                rowSpan: row.rowSpan
              }
            }
          }
        },
        {
          title: () => this.$t('apiName2'),
          dataIndex: 'title',
          width: '220px',
          scopedSlots: { customRender: 'apiName' }
        },
        {
          title: () => this.$t('desc'),
          dataIndex: 'extend.scenes'
        }
      ],
      columns2: [
        {
          title: () => this.$t('apiName2'),
          dataIndex: 'title',
          width: '220px',
          scopedSlots: { customRender: 'apiName' }
        },
        {
          title: () => this.$t('desc'),
          dataIndex: 'extend.scenes'
        }
      ]
    }
  },
  computed: {
    ...mapState('product', ['content']),
    list() {
      return utils.handlerApiList(this.content)
    },
    list2() {
      // 处理跳转链接 去掉不必要字段
      if(this.content) {
        this.content.forEach(el => {
          el.items.forEach(item => {
            let linkArr = item.location.split('/')
            item.linkLocation = `${linkArr[0]}/${linkArr[linkArr.length - 1]}`
          })
        })
      }
      if (this.content.length === 1 && this.content[0].code === 'others') {
        return this.content[0].items.map(item => {
          return {
            ...item,
            extend: {
              scenes:
                item.extend.scenes === '无\n' || item.extend.scenes === '无'
                  ? ''
                  : item.extend.scenes
            }
          }
        })
      }
      return []
    }
  }
}
</script>

<style lang="less">
.ProductApi-wrap {
  .table-link {
    cursor: pointer;
    color: #52bf63;
  }
  .header {
    border-bottom: 1px solid rgba(232, 232, 232, 1);
    padding-bottom: 12px;
    margin-bottom: 24px;
    .header-title {
      max-width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      vertical-align: bottom;
      padding-right: 4px;
      margin-bottom: 0 !important;
    }
  }
}
</style>

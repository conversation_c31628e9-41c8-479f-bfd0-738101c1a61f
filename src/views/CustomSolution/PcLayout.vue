<template>
  <div class="Product-wrap">
    <a-layout id="components-layout" class="pc">
      <div
        class="trigger-wrap"
        @click="() => (collapsed = !collapsed)"
        :style="{
          background: '#fff',
          position: 'fixed',
          zIndex: 1000,
          bottom: '50px',
          left: !collapsed ? '231px' : 0
        }"
      >
        <a-icon
          class="trigger"
          :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        />
      </div>
      <a-layout style="margin-top: 2px">
        <div
          id="pc-left-menu"
          :style="{
            width: `${scrollBarWidth + 230}px`,
            top: `${top + 1}px`
          }"
        >
          <a-layout-sider
            width="230"
            :style="{
              height: '100%',
              background: '#fff'
            }"
            v-show="!collapsed"
            :trigger="null"
            collapsible
          >
            <h3 class="title">{{ docInfo.docTitle }}</h3>
            <template v-if="menuList.length > 0">
              <a-menu
                :style="{
                  overflow: 'hidden',
                  borderRight: 0
                }"
                :inlineIndent="16"
                mode="inline"
                :default-selected-keys="[currentLocation]"
                :open-keys="openKeys"
                :selectedKeys="[currentLocation]"
                @openChange="onOpenChange"
                @click="onMenuClick"
              >
                <template v-for="item in menuList">
                  <a-menu-item
                    class="level-1"
                    v-if="!item.children || item.children.length < 1"
                    :key="item.location"
                  >
                    <span>{{ item.title }}</span>
                  </a-menu-item>
                  <PcMenuItem
                    @titleClick="titleClick"
                    v-else
                    :key="item.location"
                    :menu-info="item"
                  />
                </template>
              </a-menu>
            </template>
            <template v-else>
              <a-empty
                v-if="!loading"
                style="margin-top: 200px"
                :image="simpleImage"
              />
            </template>
          </a-layout-sider>
        </div>
        <a-layout
          :style="{
            padding: '16px',
            display: 'block',
            marginLeft: collapsed ? '0' : '232px'
          }"
        >
          <div
            :style="{
              width: `calc(100vw - ${(collapsed ? 0 : 262) +
                scrollBarWidth}px)`,
              background: '#fff',
              padding: '24px',
              paddingTop: '0'
            }"
          >
            <ApiDetail v-if="currentLocation.includes('apis/')" />
            <Detail v-else />
          </div>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>
<script>
import PcMenuItem from '@/components/PcMenuItem'
import utils from '@/utils'
import Detail from './Detail'
import ApiDetail from './ApiDetail'
import { mapState } from 'vuex'
import { Empty } from 'ant-design-vue'
export default {
  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
  },
  components: {
    Detail,
    ApiDetail,
    PcMenuItem
  },
  data() {
    return {
      collapsed: false,
      busy: false,
      openKeys: []
    }
  },
  watch: {
    upCurrentLocation: {
      handler(newValue) {
        if (newValue) this.openKeys = newValue
      },
      immediate: true
    }
  },
  computed: {
    ...mapState('solutions', [
      'menuList',
      'docInfo',
      'docNo',
      'upCurrentLocation',
      'currentLocation',
      'isApi'
    ]),
    top() {
      return this.$store.state.top
    },
    loading() {
      return this.$store.state.loading
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    }
  },
  methods: {
    toggleShowSearch() {
      this.isShowSearch = !this.isShowSearch
    },
    onOpenChange(openKeys) {
      this.openKeys = openKeys
    },
    titleClick({ key }) {
      const item = utils.findProductItem(this.menuList, key)
      const { location, hasContent } = item
      if (this.currentLocation !== location && hasContent) {
        this.$router.push(`/docs/solutions/${this.docNo}/${location}`)
      }
    },
    onMenuClick({ keyPath }) {
      const item = utils.findProductItem(this.menuList, keyPath[0])
      const { location } = item
      if (this.currentLocation === location) return
      // key 是location 将key分割 用于筛选跳转链接所需字段
      let keyPathArr = location.split('/')
      let laststr = keyPathArr[1] ? `/${keyPathArr[1]}` : ''
      let goToUrl = ''
      if(item.hasContent) {
        goToUrl = `${keyPathArr[0]}${laststr}`
      } else {
        // goToUrl = location
        // 新链接 过滤中间不需要的
        goToUrl = `${keyPathArr[0]}/${keyPathArr[keyPathArr.length - 1]}`
      }
      // this.$router.push(`/docs/solutions/${this.docNo}/${location}`)
      this.$router.push(`/docs/solutions/${this.docNo}/${goToUrl}`)
    }
  }
}
</script>
<style lang="less" scoped>
.pc-menu {
  .api-path {
    word-break: break-all;
    white-space: pre-line;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .api-name {
    word-break: break-all;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 17px;
  }
}
.search-menu {
  padding: 20px 15px 0;
}
</style>

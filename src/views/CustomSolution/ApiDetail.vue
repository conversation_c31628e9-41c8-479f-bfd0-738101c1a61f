<template>
  <a-spin :spinning="detailLoading">
    <a-row>
      <div slot="indicator">
        <img v-if="isMobile" src="@/assets/images/loading_32.gif" class="img-mobile-loading" alt />
        <img v-else src="@/assets/images/loading_64.gif" class="img-pc-loading" alt />
      </div>
      <a-col :md="24" :lg="21">
        <div class="header">
          <h1
            class="header-title"
            id="title"
            :style="{
			width: isMobile ? '100%' : 'auto',
			}"
          >{{ definition.title }}</h1>
          <div
            class="time"
            id="time"
            v-if="definition.lastModifiedDate"
          >{{ $t('updateTime') }}:{{ definition.lastModifiedDate }}</div>
          <div class="select" v-if="historyList.length !== 0">
            <Select
              style="width: 82px; height: 24px"
              v-model="version"
              @on-change="onSelectVersion"
              placement="bottom-end"
            >
              <Option
                v-for="(version, index) in historyList"
                :key="version.apiId"
                :value="version.apiId"
                :label="`Ver${historyList.length - index}.0`"
              >
                <div class="api-history-version-item">
                  <div class="api-history-version-item-text">
                    <div class="api-history-version-item-text-l">{{ `Ver${historyList.length - index}.0` }}</div>
                    <div class="api-history-version-item-text-r">
                      <template v-for="method in version.method.split(',')">
                        <div :class="method" :key="method">
                          <span class="font-size-10px">{{ method }}</span>
                        </div>
                      </template>
                      {{ version.path }}
                    </div>
                  </div>
                </div>
              </Option>
            </Select>
          </div>
          <a
            class="login-btn"
            style="float:right;height: 28px;margin-top: 8px;"
            :href="accessDiagnosisUrl"
            target="_blank"
          >
            {{ $t('accessDiagnosis') }}
          </a>
        </div>
        <div class="header-msg">
          <div class="header-msg-path">
            <template v-if="definition.method">
              <div
                :class="method"
                style="line-height: 18px; height: 18px"
                :key="method"
                v-for="method in definition.method.split(',')"
              >
                <span class="font-size-10px">{{ method }}</span>
              </div>
            </template>
            {{ definition.path }}
          </div>
          <ul class="tags-wrap">
            <li class="tags-item tags-red" v-if="definition.authority && definition.authority[0]">
              <Tooltip transfer max-width="370" :content="$t('cfcaTip')" placement="top">{{ $t('cfca') }}</Tooltip>
            </li>
            <template
              v-if="
                definition.securityReqs && definition.securityReqs.length > 0
              "
            >
              <li class="tags-item tags-yellow" v-if="getSecurity(definition.securityReqs)">
                <Tooltip
                  transfer
                  max-width="370"
                  :getPopupContainer="() => document.body"
                  :content="
                    $t('securityTip', {
                      security: getSecurity(definition.securityReqs),
                    })
                  "
                  placement="top"
                >
                  {{
                    $t('security', {
                      security: getSecurity(definition.securityReqs),
                    })
                  }}
                </Tooltip>
              </li>
            </template>
            <li
              class="tags-item tags-green"
              v-if="
                definition.securityReqs &&
                definition.securityReqs.length > 0 &&
                definition.securityReqs.find((item) => item === 'YOP-OAUTH2')
              "
            >
              <Tooltip
                transfer
                max-width="370"
                :content="$t('oauth2Tip')"
                placement="top"
              >{{ $t('oauth2') }}</Tooltip>
            </li>
            <li class="tags-item tags-blue" v-if="definition.options && definition.options.includes('IDEMPOTENT')">
              <Tooltip
                transfer
                max-width="370"
                :content="$t('midengTip')"
                placement="top"
              >{{ $t('mideng') }}</Tooltip>
            </li>
            <li class="tags-item tags-yellow" v-if="definition.options && definition.options.includes('SANDBOX')">
              {{ $t('sandbox') }}
            </li>
          </ul>
          <a-alert
            type="error"
            banner
            style="margin-bottom: 16px"
            v-if="historyList.length > 0 && version !== historyList[0].apiId"
          >
            <div slot="message">
              <i18n path="historyMsg">
                <template #action>
									<span
                    style="color: rgba(237, 51, 56, 1); cursor: pointer"
                    @click="onSelectVersion(historyList[0].apiId)"
                  >{{ $t('newVersion') }}</span>
                </template>
              </i18n>
            </div>
          </a-alert>
          <a-alert
            v-if="
              definition.type &&
              definition.type !== 'COMMON' &&
              definition.type !== 'WEB'
            "
            style="margin-bottom: 16px"
            type="info"
            :message="$t('typeTip')"
            banner
          />
        </div>
        <div
					class="text"
          style="margin-bottom: 12px;"
					v-if="definition.idempotentDesc"
					v-html="definition.idempotentDesc"
				></div>
        <div
          class="text"
          v-if="definition.desc && definition.desc !== definition.title"
          v-html="definition.desc"
        ></div>
        <h2 id="anchor1" v-if="scenesMsg">{{ $t('apiDesc') }}</h2>
        <div class="text" v-if="scenesMsg">{{ scenesMsg }}</div>
        <h2 id="anchor2" v-if="invokingMsg">{{ $t('instructions') }}</h2>
        <div class="text invoking" v-if="invokingMsg">
          <RenderMarkdown :html="invokingMsg" />
        </div>
        <h2 id="anchor3">{{ $t('repParams') }}</h2>
        <TitleToggle
          :title="$t('reqHeader')"
          :key="definition.apiId + 'common-reqParams'"
          :flag="false"
          anchor="anchor3-1"
          :tip="$t('commonRepParamsTip')"
          id="common-reqParams"
        >
          <PageTable
            id="table-common-reqParams"
            key="table-common-reqParams"
            :columns="getColumns('reqp')"
            :list="common.reqParams"
            :apiGroup="definition.apiGroup"
          ></PageTable>
        </TitleToggle>
        <h3
          id="anchor3-2"
          v-if="
            definition.reqParams && Object.keys(definition.reqParams).length > 0
          "
        >{{ definition.method.toUpperCase().includes('GET') ? $t('reqParameters') : $t('reqBody') }}</h3>
        <div
          class="select-header"
          v-if="
            definition.reqParams && Object.keys(definition.reqParams).length > 0
          "
        >
          <div class="lang">
            <div class="label">{{ $t('format') }}：</div>
            <a-radio-group v-model="reqParamsValue" v-if="Object.keys(definition.reqParams).length > 1">
              <template v-for="item in Object.keys(definition.reqParams)">
                <a-radio-button :value="item" :key="item">
                  {{
                    item
                  }}
                </a-radio-button>
              </template>
            </a-radio-group>
            <div class="text" v-else>{{ Object.keys(definition.reqParams)[0] }}</div>
          </div>
        </div>
        <PageTable
          v-if="definition.reqParams && definition.reqParams[reqParamsValue]"
          id="table-reqParams"
          :columns="getColumns('reqp')"
          :list="definition.reqParams[reqParamsValue]"
          :apiGroup="definition.apiGroup"
        ></PageTable>
        <TitleToggle
          v-if="
            definition.reqExamples && definition.reqExamples[reqParamsValue]
          "
          :key="definition.apiId + 'reqExamples'"
          :title="$t('catReqExamples')"
          anchor="anchor3-3"
          :flag="false"
          id="reqExamples"
        >
          <Code
            v-if="
              definition.reqExamples && definition.reqExamples[reqParamsValue]
            "
            :code="definition.reqExamples[reqParamsValue]"
            key="reqExamples"
          />
        </TitleToggle>
        <h2 id="anchor4">{{ $t('respParams') }}</h2>
        <TitleToggle
          :title="$t('resHeader')"
          :flag="false"
          :key="definition.apiId + 'common-respParams'"
          anchor="anchor4-1"
          :tip="$t('commonResParamsTip')"
          id="common-respParams"
        >
          <PageTable
            id="table-common-respParams"
            key="table-common-respParams"
            :columns="getColumns('resp')"
            :list="computedCommonRespParams"
            :apiGroup="definition.apiGroup"
          ></PageTable>
        </TitleToggle>
        <h3
          id="anchor4-2"
          v-if="
            definition.respParams &&
            Object.keys(definition.respParams).length > 0
          "
        >{{ $t('resBody') }}</h3>
        <div
          class="select-header"
          v-if="
            definition.respParams &&
            Object.keys(definition.respParams).length > 0
          "
        >
          <div class="lang">
            <div class="label">{{ $t('format') }}：</div>
            <a-radio-group v-model="respParamsValue" v-if="Object.keys(definition.respParams).length > 1">
              <template v-for="item in Object.keys(definition.respParams)">
                <a-radio-button :value="item" :key="item">
                  {{
                    item
                  }}
                </a-radio-button>
              </template>
            </a-radio-group>
            <div class="text" v-else>{{ Object.keys(definition.respParams)[0] }}</div>
          </div>
        </div>
        <PageTable
          v-if="definition.respParams && definition.respParams[respParamsValue]"
          id="table-respParams"
          key="table-respParams"
          :columns="getColumns('resp')"
          :list="definition.respParams[respParamsValue]"
          :apiGroup="definition.apiGroup"
        ></PageTable>
        <TitleToggle
          :title="$t('catRespExamples')"
          anchor="anchor4-3"
          :flag="false"
          id="respParams"
          :key="definition.apiId + 'respParams'"
          v-if="
            definition.respExamples && definition.respExamples[respParamsValue]
          "
        >
          <Code
            v-if="
              definition.respExamples &&
              definition.respExamples[respParamsValue]
            "
            :code="definition.respExamples[respParamsValue]"
            key="respExamples"
          />
        </TitleToggle>
        <h2 id="anchor5" v-if="showCodeExm">{{ $t('codeExm') }}</h2>
        <CodeExm v-if="showCodeExm" />
        <h2 id="anchor6">{{ $t('errorCode') }}</h2>
        <div class="text" style="margin-bottom: 12px">
          <i18n path="errorCodeTip">
            <template #action>
              <a
                target="_blank"
                href="https://open.yeepay.com/docs/v2/platform/sdk_guide/error_code/index.html"
              >{{ $t('errorCodeTipA') }}</a>
            </template>
          </i18n>
        </div>
        <ErrorCodeTable :key="definition.path" />
        <h2 id="anchor7" v-if="callbackList.length > 0">{{ $t('callback') }}</h2>
        <div class="text" style="margin-bottom: 16px" v-if="callbackList.length > 0">
          <i18n path="callbackTip1"></i18n>
          <br />
          <i18n path="callbackTip2">
            <template #action>
              <a
                target="_blank"
                href="https://open.yeepay.com/docs/platform/notify/notify-summary"
              >{{ $t('callbackTip2A') }}</a>
            </template>
          </i18n>
          <br />
          <i18n path="callbackTip3"></i18n>
        </div>
        <div class="callback-wrap" v-if="callbackList.length > 0">
          <a-tabs @tabClick="checkTabs" type="card" v-if="callbackList.length > 1">
            <a-tab-pane
              v-for="(callbackItem, index) in callbackList"
              :key="callbackItem.title"
              :tab="callbackItem.title"
              :id="`callback-${index}`"
            >
              <CallbackTable ref="callbackTable" :spiName="callbackItem.name" :currentApiId="currentApiId" :columns="getColumns()" @getBaowen="getBaowen" />
              <h4 v-if="showSpiApiData && showSpiApiData.length > 0" id="anchor10" >{{ $t('apiSearchOrder') }}</h4>
              <ApiSearchTable v-if="showSpiApiData && showSpiApiData.length > 0" key="0dlkkj"/>
            </a-tab-pane>
          </a-tabs>
          <template v-else>
            <h3>{{ callbackList[0].title }}</h3>
            <CallbackTable ref="callbackTable" :spiName="callbackList[0].name"  :currentApiId="currentApiId" :columns="getColumns()" @getBaowen="getBaowen" />
            <h4 v-if="showSpiApiData && showSpiApiData.length > 0" id="anchor10" >{{ $t('apiSearchOrder') }}</h4>
            <ApiSearchTable v-if="showSpiApiData && showSpiApiData.length > 0" key="0dlkkj"/>
          </template>
        </div>
        <template v-if="apiFaqList.length > 0">
          <h2 id="anchor8">{{$t('menu.questionTitle')}}</h2>
          <div class="faq-wrap">
            <FaqItem
              v-for="item in apiFaqList"
              :key="item.id"
              :item="item"
            />
          </div>
        </template>
      </a-col>
      <a-col :md="0" :lg="3" style="padding-left: 20px" v-if="!isMobile">
        <a-anchor
          @click="handleClick"
          @change="onAnchorChange"
          :wrapperStyle="{
            paddingTop: '20px',
          }"
        >
          <a-anchor-link href="#anchor1" :title="$t('apiDesc')" v-if="scenesMsg" />
          <a-anchor-link href="#anchor2" :title="$t('instructions')" v-if="invokingMsg" />
          <a-anchor-link
            href="#anchor3"
            :title="$t('repParams')"
            :class="{
              'api-anchor-wrap':
                currentAnchor !== '#anchor3' &&
                currentAnchor.indexOf('#anchor3') !== -1,
            }"
          >
            <a-anchor-link href="#anchor3-1" :title="$t('reqHeader')" />
            <a-anchor-link
              href="#anchor3-2"
              v-if="
                definition.reqParams &&
                Object.keys(definition.reqParams).length > 0
              "
              :title="definition.method.toUpperCase().includes('GET') ? $t('reqParameters') : $t('reqBody')"
            />
            <a-anchor-link
              v-if="
                definition.reqExamples && definition.reqExamples[reqParamsValue]
              "
              href="#anchor3-3"
              :title="$t('catReqExamples')"
            />
          </a-anchor-link>
          <a-anchor-link
            href="#anchor4"
            :title="$t('respParams')"
            :class="{
              'api-anchor-wrap':
                currentAnchor !== '#anchor4' &&
                currentAnchor.indexOf('#anchor4') !== -1,
            }"
          >
            <a-anchor-link href="#anchor4-1" :title="$t('resHeader')" />
            <a-anchor-link
              href="#anchor4-2"
              v-if="
                definition.respParams &&
                Object.keys(definition.respParams).length > 0
              "
              :title="$t('resBody')"
            />
            <a-anchor-link
              v-if="
                definition.respExamples &&
                definition.respExamples[respParamsValue]
              "
              href="#anchor4-3"
              :title="$t('catRespExamples')"
            />
          </a-anchor-link>
          <a-anchor-link href="#anchor5" v-if="showCodeExm" :title="$t('codeExm')" />
          <a-anchor-link href="#anchor6" :title="$t('errorCode')" />
          <a-anchor-link
            href="#anchor7"
            v-if="callbackList.length > 0"
            :title="$t('callback')"
            :class="{
                'api-anchor-wrap':
                  currentAnchor !== '#anchor7' &&
                  currentAnchor.indexOf('#anchor7') !== -1,
              }"
          >
            <a-anchor-link href="#anchor7-1" :title="$t('tongzhicanshu')" />
            <a-anchor-link href="#anchor7-2" v-if="isShowBaowen" :title="$t('shilibaowen')" />
            <a-anchor-link v-if="showSpiApiData && showSpiApiData.length > 0" href="#anchor10" :title="$t('apiSearchOrder')" />
          </a-anchor-link>
          <a-anchor-link href="#anchor8"  v-if="apiFaqList.length > 0" :title="$t('menu.questionTitle')">
            <a-anchor-link v-for="faq in apiFaqList" :key="faq.id" :href="`#${faq.title}faq`" :title="faq.title" />
          </a-anchor-link>
        </a-anchor>
      </a-col>
    </a-row>
  </a-spin>
</template>

<script>
import Code from '@/components/Code'
import CodeExm from '@/components/CodeExm'
import CallbackTable from '@/components/CallbackTable'
import PageTable from '@/components/PageTable'
import ErrorCodeTable from '@/components/ErrorCodeTable/other.vue'
import ApiSearchTable from '@/components/ApiSearchTable'
import TitleToggle from '@/components/TitleToggle'
import FaqItem from '@/components/FaqItem'
import { Select, Option } from 'view-design'
import RenderMarkdown from '@/components/RenderMarkdown'
import { mapState } from 'vuex'
import utils from '@/utils'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    Select,
    Code,
    CodeExm,
    CallbackTable,
    RenderMarkdown,
    PageTable,
    ErrorCodeTable,
    ApiSearchTable,
    TitleToggle,
    FaqItem,
    Option,
    [Tabs.name]: Tabs,
    [Tabs.TabPane.name]: Tabs.TabPane,
  },
  data() {
    return {
      version: '',
      currentAnchor: '',
      isShowBaowen: false,
      respParamsValue: 'application/json',
      reqParamsValue: 'application/x-www-form-urlencoded',
      showSpiApiData: [],
      apiBasicData: {},
      spiApiMapData: {},
      hrefMap: {
        'error-code': '#anchor6',
        'error-msg': '#anchor6',
        'notify-url': '#anchor7'
      }
    }
  },
  computed: {
    ...mapState('solutions', [
      'subMenuApiId',
      'currentApiId',
      'definition',
      'pageNo',
      'common',
      'resHeader',
      'scenesMsg',
      'invokingMsg',
      'menuList',
      'itemsList',
      'historyList',
      'detailLoading',
      'callbackList',
      'apiFaqList',
    ]),
    isMobile() {
      return this.$store.state.isMobile
    },
    subMenuList() {
      return this.$store.getters.subMenuList
    },
    showCodeExm() {
      const { sampleCodes = {} } = this.definition
      return Object.keys(sampleCodes).length > 0
    },
    computedCommonRespParams() {
      if (!this.resHeader[this.respParamsValue]) {
        return this.common.respParams
      }
      if (!this.common.respParams) {
        return this.resHeader[this.respParamsValue]
      }
      return this.common.respParams.concat(this.resHeader[this.respParamsValue])
    },
    accessDiagnosisUrl() {
      const hostname = window.location.hostname
      if (hostname === 'open.yeepay.com') {
        return `https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis?apiId=${this.currentApiId}`
      }
      return `https://qamp.yeepay.com/auth/signin?redirectUrl=https://qamp.yeepay.com/yop-developer-center/cas?redirectUrl=https://qamp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis?apiId=${this.currentApiId}`
    },
  },
  watch: {
    'definition.reqParams': function (newValue) {
      if (!newValue) return
      this.reqParamsValue = Object.keys(newValue)[0]
    },
    currentApiId: {
      handler(newValue) {
        this.version = newValue
      },
      immediate: true,
    },
    'definition.respParams': function (newValue) {
      if (!newValue) return
      this.respParamsValue = Object.keys(newValue)[0]
    },
    '$store.state.apiDocs.showSpiApiData': {
      handler(newValue) {
        this.showSpiApiData = newValue
      },
      immediate: true
    },
    // api 基本信息json
    '$store.state.apiDocs.apiBasicData': {
      handler(newValue) {
        this.apiBasicData = newValue
      },
      immediate: true
    },
    '$store.state.apiDocs.spiApiMapData': {
      handler(newValue) {
        this.spiApiMapData = newValue
      },
      immediate: true
    },
  },
  methods: {
    checkTabs(row) {
      let curSpi = this.callbackList.filter((element) => element.title === row)
      console.log(curSpi[0])
      let spiName = curSpi[0].name
      // 直接读取vuex 数据 筛选对应表单的内容
      let currShowSpiApiData = []
      if(this.spiApiMapData[spiName]) {
        for (let key in this.apiBasicData) {
          this.spiApiMapData[spiName].forEach((item) => {
            if(key === item) {
              currShowSpiApiData.push(this.apiBasicData[key])
            }
          })
        }
      }
      this.$store.commit('apiDocs/setShowSpiApiData', currShowSpiApiData)
    },
    getColumns(type) {
      const columns = [
        {
          renderHeader: (h) => h('span', this.$t('tableColumns.title1')),
          key: 'name',
          tree: true,
          width: 200,
        },
        {
          renderHeader: (h) => h('span', this.$t('tableColumns.title2')),
          key: 'type',
          width: 200,
          render: (h, scope) => {
            const { type, maxLength } = scope.row
            if (maxLength && maxLength !== '-') {
              return h('div', [h('span', type), h('span', `(${maxLength})`)])
            }
            return h('span', type)
          },
        },
        {
          renderHeader: (h) => h('span', this.$t('tableColumns.title3')),
          key: 'required',
          width: 100,
          render: (h, scope) => {
            const required = scope.row.required
            if (required) {
              return h('span', this.$t('yes'))
            }
            return h('span', this.$t('no'))
          },
        },
        {
          renderHeader: (h) => h('span', this.$t('tableColumns.title4')),
          key: 'desc',
          minWidth: 325,
          render: (h, scope) => {
            const content = scope.row.computedDesc === '未命名' ? '-' : scope.row.computedDesc
            const list = [
              h('div',
                {
                  class: 'api-desc-wrap',
                  domProps: {
                    innerHTML: content,
                  },
                })
            ]
            if (scope.row.example && scope.row.example !== '-') {
              const tip = scope.row.defVal ? '默认值' : this.$t('tableColumns.tip')
              list.push(
                h(
                  'div',
                  {
                    style: { color: '#61affe' },
                  },
                  `${tip}：${scope.row.example}`
                )
              )
            }
            if (scope.row.format && scope.row.format !== '-' && this.hrefMap[scope.row.format]) {
              list.push(
                h(
                  'a',
                  {
                    domProps: {
                      href: this.hrefMap[scope.row.format]
                    },
                  },
                  '查看详情'
                )
              )
            }
            return h('div', list)
          },
        },
      ]
      if(type === 'resp' || type === 'reqp') {
        const col = {
          renderHeader: (h) => h('Poptip', 
            {
              style: { 
                // 'margin-left': '4px', 
                'cursor': 'pointer',
              },
              props: {
                'class-name': 'secreat',
                placement: 'top',
                confirm: false,
                trigger: 'hover',
                transfer: true,
                title: ''
              },
            },
            [
              h('div', {
                slot: 'content'
              }, [
                  h('p', [
                    h('span', '仅使用商密且Java SDK 4.3.0版本以上的商户支持加密。')
                  ]),
                  h('p', [
                    h('span', '若您需要加密，请查看'),
                    h('a', {
                      attrs:{
                        href:'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/java-sdk-guide-sm',
                        target: '_blank'
                      }
                    },'SDK使用说明，'),
                    h('span', '其他情况无须处理。'),
                  ]),
                  h('p', [
                    h('span', '若您想自实现加密逻辑或使用自实现的SDK，请参考'),
                    h('a', {
                      attrs:{
                        href:'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/msg_encrypt',
                        target: '_blank'
                      }
                    },'报文加密机制。'),
                  ])
              ]),
              h('span', this.$t('tableColumns.title5')),
							h('a-icon', {
								style: { cursor: 'pointer', 'margin-left': '4px'},
								props: {
									type: 'question-circle'
								}
							})
            ]),
          key: 'encrypt',
          width: 100,
          render: (h, scope) => {
            const encrypt = scope.row.encrypt
            if (encrypt) {
              return h('span', this.$t('recommend'))
            }
            return h('span', this.$t('unnecessary'))
          },
        }
        columns.splice(3, 0, col)
      }
      if(type === 'resp') {
        columns.splice(2, 1)
      }
      return columns
    },
    getSecurity(list) {
      const newList = list
        .map((item) => {
          return item.split('-')[1]
        })
        .filter((item) => item !== 'OAUTH2')
      return newList.join('、')
    },
    onSelectVersion(value) {
      this.version = value
      const api = utils.findHistoryApi(this.itemsList, value)
      this.$store.dispatch('solutions/init', {
        apiId: value,
        spiCount: api.spiCount,
      })
    },
    handleClick(e) {
      e.preventDefault()
    },
    onAnchorChange(currentAnchor) {
      this.currentAnchor = currentAnchor
    },
    getBaowen(show) {
      this.isShowBaowen = show
    },
  },
}
</script>
<style lang="less">
.api-desc-wrap {
  ul > li{ list-style:disc inside !important;}
  ol > li{ list-style:decimal inside !important;}
}
.ivu-table-wrapper {
  .htip {
    width: 14px;
    height: 16px;
    display: inline-block;
    line-height: 16px;
    vertical-align: middle;
    cursor: pointer;
    margin-left: 4px;
    svg {
      width: 14px;
      height: 14px;
    }
  }
  .ivu-table-row-hover {
    td {
      background: rgba(0, 0, 0, 0.02) !important;
    }
  }
  .ivu-table-cell-tree {
    border: none;
    background: transparent;
    width: 12px;
    margin-left: -12px;
    height: inherit;
  }
  .ivu-icon-ios-add:before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    margin-top: -3px;
    border: 4px solid transparent;
    border-left-color: rgba(0, 0, 0, 0.35);
    vertical-align: middle;
  }
  .ivu-icon-ios-remove:before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.35);
  }
}
.invoking {
  .vditor-reset p {
    margin-bottom: 0;
    line-height: 22px;
  }
}
</style>
<style lang="less" scoped>
.header {
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  padding-bottom: 12px;
  margin-bottom: 20px;
  overflow: auto;
  .header-title {
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: bottom;
    margin-right: 16px;
    margin-bottom: 0 !important;
  }
  h1 {
    display: inline-block;
  }
  .time {
    height: 12px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 12px;
    display: inline-block;
    vertical-align: super;
  }
  .select {
    float: right;
    .api-history-version-item {
      box-sizing: border-box;
      min-height: 32px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
      font-weight: 400;
      line-height: 32px;
      padding-left: 12px;
      cursor: pointer;
    }
    .api-history-version-item-text {
      box-sizing: border-box;
      display: inline-block;
      vertical-align: middle;
      margin-right: 6px;
      box-sizing: border-box;
    }
    .api-history-version-item-text-l {
      width: 44px;
      box-sizing: border-box;
      display: inline-block;
      vertical-align: text-top;
      height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
      font-weight: 400;
      line-height: 22px;
    }
    .api-history-version-item-text-r {
      display: inline-block;
      line-height: 22px;
    }
    /deep/ .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
    .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
      height: 24px;
      line-height: 24px;
    }
    /deep/ .ivu-select-single .ivu-select-selection {
      height: 24px;
      line-height: 24px;
    }
    /deep/ .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
      height: 24px;
      line-height: 24px;
    }
  }
}
.header-msg {
  margin-bottom: 12px;
  .header-msg-path {
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serif;
    font-weight: 500;
    color: #333333;
    line-height: 18px;
    margin-bottom: 12px;
    word-break: break-all;
  }
  .tags-wrap {
    margin-bottom: 12px;
    .tags-item {
      margin-right: 4px;
      cursor: pointer;
      display: inline-block;
      vertical-align: top;
    }
    .tags-red {
      border: 1px solid #f46266;
      color: #f46266;
    }
    .tags-yellow {
      border: 1px solid #fdbb17;
      color: #fdbb17;
    }
    .tags-blue {
      border: 1px solid #61affe;
      color: #61affe;
    }
    .tags-green {
      border: 1px solid #49cc90;
      color: #49cc90;
    }
    .tags-red,
    .tags-yellow,
    .tags-blue,
    .tags-green {
      height: 18px;
      border-radius: 2px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
      font-weight: 400;
      line-height: 18px;
      padding: 0px 6px;
      :hover {
        opacity: 0.8;
      }
    }
  }
}
.callback-wrap {
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin-bottom: 0;
    }
    .ant-tabs-content {
      border: 1px solid #e8e8e8;
      border-top: none;
      padding: 16px;
      padding-top: 20px;
    }
    .ant-tabs-tab {
      margin-right: 15px;
    }
    .ant-tabs-tab-active::before {
      border-top-color: #52bf63;
    }
  }
}
</style>

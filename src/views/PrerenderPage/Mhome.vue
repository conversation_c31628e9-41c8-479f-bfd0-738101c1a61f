<template>
  <div class="mobile-home-wrap">
    <div class="header-wrap">
      <div class="title">易宝开放平台 为开发者赋能</div>
      <div class="sub-title">标准化、全场景覆盖、提升企业整体竞争力</div>
    </div>
    <div class="solution-wrap">
      <div class="title">解决方案</div>
      <div
        class="solution-list-wrap"
        id="solution-list-wrap-mobile"
        @touchstart="touchstart"
        @touchend="touchend"
        @scroll.passive="onScroll"
      >
        <ul class="solution-list">
          <li v-for="(item, index) in solutionList" :key="index">
            <div
              class="solution-item"
              :class="{ 'solution-item-scalc': currentSolution !== index }"
            >
              <img :src="item.content.imageUrl" alt="" />
              <div class="content-wrap">
                <template v-if="index === 0">
                  <div class="fist-item" style="margin-bottom: 20px">
                    <div class="pay-item">
                      <a :href="item.content.list[0].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/standard.png"
                          alt=""
                        />
                        <div class="text">标准商户收付</div>
                      </a>
                    </div>
                  </div>
                  <div class="fist-item">
                    <div class="pay-item" style="margin-right: 58px">
                      <a :href="item.content.list[1].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/platform.png"
                          alt=""
                        />
                        <div class="text">平台商收付</div>
                      </a>
                    </div>
                    <div class="pay-item">
                      <a :href="item.content.list[2].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/service.png"
                          alt=""
                        />
                        <div class="text">服务商收付</div>
                      </a>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="content-title">
                    {{ item.content.title }}
                  </div>
                  <div class="content-desc">
                    {{ item.content.desc }}
                  </div>
                  <a :href="item.content.linkUrl" class="solution-item-link"
                    >查看详情 ></a
                  >
                </template>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="solution-icon-wrap" id="solution-icon-wrap-mobile">
        <ul class="solution-icon-list">
          <li
            class="solution-icon-item"
            v-for="(item, index) in solutionList"
            :class="{ 'solution-icon-active': currentSolution === index }"
            @click="onIconClick(index)"
            :key="index"
          >
            <span
              class="iconfont icon"
              :style="{ fontSize: item.iconSize }"
              v-html="item.icon"
            ></span>
          </li>
        </ul>
      </div>
    </div>
    <div class="recommend-app">
      <div class="title">推荐应用</div>
      <ul class="app-list">
        <li
          class="app-item"
          v-for="(item, index) in recommendAppList"
          :class="{ active: active === index }"
          @click="changeApp(index)"
          :key="item.name"
        >
          {{ item.name }}
        </li>
      </ul>
      <div
        class="recommend-content-wrap"
        id="recommend-content-wrap"
        @scroll="onRecommendScroll"
      >
        <ul class="recommend-content">
          <li
            class="recommend-content-item"
            v-for="(recommendAppContent, index) in recommendAppList"
            :key="index"
          >
            <div class="img-wrap">
              <img :src="recommendAppContent.img" alt="" />
            </div>
            <div class="offine-title-desc">
              {{ recommendAppContent.content.subTitle }}
            </div>
            <ul class="offine-pord-list">
              <li
                class="offine-pord-item"
                v-for="item in recommendAppContent.content.iconList"
                :key="item.text"
              >
                <a :href="item.contentUri" style="display: block">
                  <img :src="item.imgUrl" alt="" />
                  <div class="text">{{ item.text }}</div>
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
    <div class="api-wrap">
      <div class="develop-tip">专为开发人员设计</div>
      <div class="develop-title">强大且易用的API</div>
      <div class="develop-img-wrap">
        <img
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
      </div>
      <div class="develop-desc">
        我们来处理那些抽象的事物，让您的团队免去整合不同系统的麻烦，也不需要花费数月时间来集成支付功能。
      </div>
      <div class="desc-item" style="margin-bottom: 40px">
        <div class="desc-item-title">多种语言版本的SDK工具</div>
        <div class="desc-item-sub-title" style="margin-bottom: 8px">
          我们准备了从 Java、Go、Python、PHP、.NET、NodeJs
          等多种语言的SDK，方便你的快速接入。
        </div>
        <a class="link" href="/docs/platform/sdk_guide/sdk-guide">查看 ></a>
      </div>
      <div class="desc-item">
        <div class="desc-item-title">便捷的密钥工具</div>
        <div class="desc-item-sub-title" style="margin-bottom: 8px">
          可以方便你快速生成接入所需的密钥，并能安全下载并激活CFCA证书文件。
        </div>
        <a class="link" href="/docs/platform/developTools/keyTools">查看 ></a>
      </div>
    </div>
    <div class="data-wrap">
      <div class="data-tip">安全、合规、稳定、全面的交易服务</div>
      <div class="data-title">为企业提供强有力的支持 助力业务飞速拓展</div>
      <ul>
        <li class="desc-item">
          <div class="desc-item-title">2.6亿+</div>
          <div class="desc-item-sub-title">
            每天API请求次数，峰值达1,000每秒
          </div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">2,000个+</div>
          <div class="desc-item-sub-title">API服务接口</div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">30个+</div>
          <div class="desc-item-sub-title">交易服务解决方案与产品服务</div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">99.9%+</div>
          <div class="desc-item-sub-title">交易服务可用性</div>
        </li>
      </ul>
    </div>
    <div class="brand-wrap">
      <div class="brand-background"></div>
      <div class="brand-title">超过 100+企业正在使用易宝开放平台</div>
      <div class="brand-list">
        <div class="brand-item" v-for="item in 24" :key="item">
          <img
            :src="`https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/${item}.png`"
            alt=""
          />
        </div>
      </div>
      <div class="brand-start">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="title">准备好开始了？</div>
        <div class="title-desc">
          与我们销售取得联系，签约并入网，联调成功后，就可以收款了。我们也可以为您的公司量身制定最合适的套餐。
        </div>
        <div class="link-wrap">
          <a
            class="link"
            href="https://www.yeepay.com/customerService/businessCooperation"
          >
            联系销售 >
          </a>
        </div>
      </div>
      <div class="item" style="margin-bottom: 10px">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="text">
          <div class="text-title">开始你的集成</div>
          <div class="text-desc">
            若你还未签约入网成功或正在入网中，你现在根据需要了解平台的API。待服务开通后，就可以开始联调与部署工作了。
          </div>
          <a class="text-link" href="/docs">全部API ></a>
        </div>
      </div>
      <div class="item">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="text">
          <div class="text-title">成为平台伙伴</div>
          <div class="text-desc">
            若你有足够的实力，想将你的服务集成到本平台，请留下你的联系信息与服务内容，我们收到信息后会及时与你联系。
          </div>
          <a
            class="text-link"
            href="https://www.yeepay.com/customerService/businessCooperation"
            >成为伙伴 ></a
          >
        </div>
      </div>
    </div>
    <div class="footer-link">
      <ul class="link-list">
        <li
          class="link-item"
          :class="{ showSubLink: showSub === index }"
          v-for="(item, index) in footerLinkList"
          :key="item.title"
        >
          <div class="link-item-content" @click="onFooterLinkClick(index)">
            <div class="text">{{ $t(item.title) }}</div>
            <a-icon
              :style="{ fontSize: '10px' }"
              :type="showSub === index ? 'down' : 'right'"
            />
          </div>
          <ul class="footer-sub-link">
            <li
              class="footer-sub-link-item"
              v-for="item in item.children"
              :key="item.title"
            >
              <a :href="item.contentUri">{{ $t(item.title) }}</a>
            </li>
          </ul>
        </li>
      </ul>
      <div class="phone-msg">{{ $t('serviceHotline') }}</div>
      <div class="phone-num">
        <div class="left">
          <div class="num">{{ $t('phonenum') }}</div>
          <div class="desc">{{ $t('phonenumsub') }}</div>
        </div>
      </div>
      <ul class="zizhi-wrap">
        <li class="zizhi-item" v-for="(item, index) in zizhiList" :key="index">
          <template v-if="item === 'javascript:void(0)'">
            <a :href="item" :class="`bg${index + 1}`"></a>
          </template>
          <template v-else>
            <a :href="item" :class="`bg${index + 1}`" target="_blank"></a>
          </template>
        </li>
      </ul>
    </div>
    <div class="footer">
      {{ $t('footerText') }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      active: 0,
      showSub: null,
      currentSolution: 0,
      onTouch: false,
      timer: null,
      listWrap: null,
      iconWrap: null,
      zizhiList: [
        'https://www.yeepay.com/paymentLicense',
        'https://www.yeepay.com/businessLicense',
        'javascript:void(0)',
        'https://www.yeepay.com/yeepaypci',
        'http://www.cyberpolice.cn/wfjb/',
        'https://ss.knet.cn/verifyseal.dll?sn=2011051700100008785&ct=df&a=1&pa=0.761116063861185',
        'javascript:void(0)',
      ],
    }
  },
  computed: {
    footerLinkList() {
      return this.$store.state.footerLinkList
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    solutionList() {
      return this.$store.state.solutionList
    },
    recommendAppList() {
      return this.$store.state.recommendAppList
    },
  },
  created() {
    if (window.__PRERENDER_INJECTED && window.__PRERENDER_INJECTED.pre) return
    if (!this.isMobile) this.$router.replace('/home')
  },
  mounted() {
    this.listWrap = document.querySelector('#solution-list-wrap-mobile')
    this.recommendListWrap = document.querySelector('#recommend-content-wrap')
    this.clientWidth = document.documentElement.clientWidth - 30
  },
  methods: {
    onFooterLinkClick(index) {
      if (this.showSub === index) {
        this.showSub = null
        return
      }
      this.showSub = index
    },
    changeApp(index) {
      this.active = index
      this.recommendListWrap.scrollLeft =
        document.documentElement.clientWidth * index
    },
    onIconClick(index) {
      this.currentSolution = index
      this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
    },
    touchstart() {
      this.onTouch = true
    },
    touchend() {
      this.onTouch = false
      if (this.timer) return
      const scrollLeft = this.listWrap.scrollLeft
      const index = Math.round(scrollLeft / this.clientWidth)
      this.currentSolution = index
      this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
    },
    onScroll() {
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        clearTimeout(this.timer)
        this.timer = null
        if (this.onTouch) return
        const scrollLeft = this.listWrap.scrollLeft
        const index = Math.round(scrollLeft / this.clientWidth)
        this.currentSolution = index
        this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
      }, 500)
    },
    onRecommendScroll() {
      const scrollLeft = this.recommendListWrap.scrollLeft + 50
      const index = Math.floor(
        scrollLeft / document.documentElement.clientWidth
      )
      this.active = index
    },
  },
}
</script>

<style lang="less" scoped>
.mobile-home-wrap {
  background-color: #fff;
  .header-wrap {
    height: 430px;
    background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
      no-repeat center;
    background-size: 100% 430px;
    padding-top: 321px;
    .title {
      height: 36px;
      font-size: 25px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 36px;
      text-align: center;
      margin-bottom: 10px;
    }
    .sub-title {
      height: 18px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
      line-height: 18px;
      text-align: center;
    }
  }
  .solution-wrap {
    padding-top: 40px;
    .title {
      height: 30px;
      font-size: 22px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.65);
      line-height: 30px;
      text-align: center;
      margin-bottom: 40px;
    }
    .solution-list-wrap {
      width: 100vw;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      .solution-list {
        white-space: nowrap;
        float: left;
        padding: 0 20px 20px;
        display: flex;
        align-items: center;
        overflow: hidden;
        li {
          width: calc(100vw - 40px);
          height: 528px;
          margin-right: 10px;
          display: flex;
          align-items: center;
        }
        .solution-item {
          width: calc(100vw - 40px);
          height: 528px;
          background: #ffffff;
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
          border-radius: 10px;
          .solution-item-link {
            display: block;
            text-align: center;
            height: 26px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
              SimSun, sans-serif;
            font-weight: 500;
            color: #fff;
            background: #52bf63;
            width: 110px;
            margin: 0 auto;
            border-radius: 13px;
            line-height: 26px;
          }
          img {
            width: calc(100vw - 40px);
            height: 235px;
          }
          .content-wrap {
            padding: 36px 20px 0;
            .fist-item {
              display: flex;
              justify-content: center;
              .pay-item {
                text-align: center;
                img {
                  width: 72px;
                  height: 72px;
                }
                .text {
                  height: 21px;
                  font-size: 15px;
                  font-family: PingFangSC-Regular, PingFang SC,
                    'Microsoft YaHei', SimSun, sans-serif;
                  font-weight: 400;
                  color: rgba(0, 0, 0, 0.85);
                  line-height: 21px;
                  text-align: center;
                }
              }
            }
            .content-title {
              height: 25px;
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.85);
              line-height: 25px;
              text-align: center;
              margin-bottom: 20px;
            }
            .content-desc {
              height: 80px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.45);
              line-height: 20px;
              text-align: center;
              white-space: pre-wrap;
              margin-bottom: 42px;
            }
            .content-img {
              width: 100%;
              height: auto;
            }
          }
        }
        .solution-item-scalc {
          width: calc(100vw - 40px);
          height: 500px;
        }
      }
    }
    .solution-icon-wrap {
      width: 100vw;
      text-align: center;
      height: 50px;
      line-height: 50px;
      .solution-icon-list {
        display: inline-flex;
        align-items: center;
        height: 50px;
        line-height: 50px;
        .solution-icon-item {
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          .icon {
            font-size: 12px;
          }
        }
        .solution-icon-active {
          background: #f7f9fc;
          box-shadow: 0px 3px 5px 0px #e6e6e6;
          border-radius: 50%;
          .icon {
            color: #52bf63;
          }
        }
      }
    }
  }
  .recommend-app {
    background-color: #f4f7fd;
    padding: 70px 0;
    position: relative;
    &::before {
      content: '';
      height: 100px;
      border-left: 100vw /2 solid #fff;
      border-top: 50px solid #fff;
      border-bottom: 50px solid transparent;
      border-right: 100vw /2 solid transparent;
      position: absolute;
      left: 0;
      top: 0;
    }
    &::after {
      content: '';
      height: 100px;
      border-right: 100vw /2 solid #fff;
      border-bottom: 50px solid #fff;
      border-top: 50px solid transparent;
      border-left: 100vw /2 solid transparent;
      position: absolute;
      left: 0;
      bottom: 0;
    }
    .title {
      height: 30px;
      font-size: 22px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.65);
      line-height: 30px;
      text-align: center;
      margin-bottom: 21px;
    }
    .app-list {
      margin-bottom: 52px;
      display: flex;
      justify-content: center;
      .app-item {
        width: 60px;
        height: 21px;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 21px;
        margin-right: 30px;
        &:last-child {
          margin-right: 0;
        }
      }
      .active {
        font-weight: 500;
        color: #52bf63;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 32px;
          width: 64px;
          height: 2px;
          background: #52bf63;
        }
      }
    }
    .recommend-content-wrap {
      width: 100vw;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }
    .recommend-content {
      display: flex;
      float: left;
      .recommend-content-item {
        width: 100vw;
        flex-shrink: 0;
      }
    }
    .img-wrap {
      padding: 0 30px;
      margin-bottom: 10px;
      img {
        width: 100%;
      }
    }
    .offine-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 20px;
    }
    .offine-title-desc {
      padding: 0 55px 0 45px;
      text-align: center;
      margin-bottom: 36px;
    }
    .offine-pord-list {
      display: flex;
      justify-content: center;
      .offine-pord-item {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        line-height: 20px;
        margin-right: 52px;
        display: flex;
        justify-content: center;
        width: 64px;
        &:last-child {
          margin-right: 0;
        }
        img {
          width: 64px;
          height: 64px;
          margin-bottom: 12px;
        }
        .text {
          width: 64px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          text-align: center;
          white-space: pre-wrap;
        }
      }
    }
  }
  .api-wrap {
    .develop-img-wrap {
      padding: 0 60px;
      margin-bottom: 41px;
      img {
        width: 100%;
      }
    }
    .develop-tip {
      height: 14px;
      font-size: 10px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 14px;
      text-align: center;
      margin-bottom: 10px;
    }
    .develop-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 20px;
    }
    .develop-desc {
      padding: 0 50px;
      height: 60px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.55);
      line-height: 20px;
      text-align: center;
      margin-bottom: 40px;
    }
    .desc-item {
      padding-left: 58px;
      padding-right: 50px;
      .desc-item-title {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        margin-bottom: 10px;
        position: relative;
        &::before {
          content: '';
          width: 2px;
          height: 14px;
          background: #52bf63;
          border-radius: 1px;
          position: absolute;
          left: -6px;
          top: 3px;
        }
      }
      .desc-item-sub-title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        line-height: 20px;
      }
    }
  }
  .data-wrap {
    background: #041832;
    padding-top: 130px;
    padding-left: 60px;
    padding-bottom: 60px;
    position: relative;
    &::before {
      content: '';
      height: 100px;
      border-left: 100vw /2 solid #fff;
      border-top: 50px solid #fff;
      border-bottom: 50px solid transparent;
      border-right: 100vw /2 solid transparent;
      position: absolute;
      left: 0;
      top: -1px;
    }
    .data-tip {
      height: 17px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
      line-height: 17px;
      margin-bottom: 10px;
    }
    .data-title {
      width: 201px;
      height: 58px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      margin-bottom: 56px;
    }
    .desc-item {
      padding-left: 15px;
      position: relative;
      margin-bottom: 30px;
      &:last-child {
        margin-bottom: 0px;
      }
      &::before {
        content: '';
        width: 3px;
        height: 30px;
        background: #52bf63;
        border-radius: 1px;
        position: absolute;
        left: 0;
        top: 7px;
      }
      .desc-item-title {
        height: 45px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #ffffff;
        line-height: 45px;
        margin-bottom: 16px;
      }
      .desc-item-sub-title {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.65);
        line-height: 20px;
      }
    }
  }
  .brand-wrap {
    padding: 60px 20px;
    background: #f4f7fd;
    position: relative;
    z-index: 1;
    .brand-background {
      height: 436px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      background: #fff;
      z-index: -1;
      &::after {
        content: '';
        height: 100px;
        border-right: 100vw /2 solid #f4f7fd;
        border-bottom: 50px solid #f4f7fd;
        border-top: 50px solid transparent;
        border-left: 100vw /2 solid transparent;
        position: absolute;
        left: 0;
        bottom: 0;
      }
    }
    .brand-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 30px;
    }
    .brand-list {
      display: flex;
      margin-right: -10px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 61px;
      .brand-item {
        width: 105px;
        height: 37px;
        background: #ffffff;
        border-radius: 2px;
        margin-right: 10px;
        margin-bottom: 11px;
        img {
          width: 105px;
          height: 37px;
        }
      }
    }
    .brand-start {
      height: 413px;
      background: #ffffff;
      box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      padding: 40px 20px;
      margin-bottom: 20px;
      .img {
        height: 106px;
        display: block;
        margin: 0 auto 40px;
      }
      .title {
        height: 30px;
        font-size: 22px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 30px;
        text-align: center;
        margin-bottom: 20px;
      }
      .title-desc {
        height: 60px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        text-align: center;
        margin-bottom: 40px;
      }
      .link-wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        .link-btn {
          width: 88px;
          height: 32px;
          background: #52bf63;
          border-radius: 16px;
          margin-right: 34px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);
          line-height: 32px;
          text-align: center;
        }
        .link {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #52bf63;
          line-height: 20px;
        }
      }
    }
    .item {
      background: #ffffff;
      box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: flex-start;
      padding: 20px;
      .img {
        height: 65px;
        margin-right: 26px;
      }
      .text {
        text-align: left;
        .text-title {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          margin-bottom: 8px;
        }
        .text-desc {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 17px;
          margin-bottom: 10px;
        }
        .text-link {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #52bf63;
          line-height: 20px;
        }
      }
    }
  }
  .footer-link {
    background-color: #fff;
    .link-list {
      margin-bottom: 40px;
      .link-item {
        .link-item-content {
          padding: 15px 20px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 22px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .text {
            color: rgba(0, 0, 0, 0.85);
          }
        }
        .footer-sub-link {
          display: none;
          background: #fafafa;
          padding: 20px 0;
          .footer-sub-link-item {
            line-height: 22px;
            margin-bottom: 20px;
            &:last-child {
              margin-bottom: 0;
            }
            a {
              padding-left: 40px;
              color: rgba(0, 0, 0, 0.65);
            }
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      .showSubLink {
        .link-item-content {
          .text {
            color: #52bf63;
          }
        }
        .footer-sub-link {
          display: block;
        }
      }
    }
    .phone-msg {
      height: 21px;
      font-size: 15px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #000000;
      line-height: 21px;
      margin-bottom: 10px;
      padding-left: 20px;
    }
    .phone-num {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 25px;
      padding-left: 20px;
      .left {
        display: flex;
        align-items: center;
        .num {
          margin-right: 5px;
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #000000;
          line-height: 28px;
        }
        .msg {
          height: 17px;
          font-size: 12px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #000000;
          line-height: 17px;
        }
      }
      .img-feishu-weixin {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #efefef;
        margin-right: 7px;
      }
    }
    .zizhi-wrap {
      display: flex;
      padding-left: 20px;
      .zizhi-item {
        margin-right: 4px;
        a {
          display: block;
          height: 21px;
          background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
            no-repeat;
          background-size: 248px 21px;
        }
        .bg1 {
          width: 25px;
        }
        .bg2 {
          width: 30px;
          background-position-x: -29px;
        }
        .bg3 {
          width: 30px;
          background-position-x: -62px;
        }
        .bg4 {
          width: 25px;
          background-position-x: -100px;
        }
        .bg5 {
          width: 20px;
          background-position-x: -128px;
        }
        .bg6 {
          width: 41px;
          background-position-x: -150px;
        }
        .bg7 {
          width: 55px;
          background-position-x: -194px;
        }
      }
    }
    .zizhi {
      height: 21px;
    }
  }
  .footer {
    width: 100vw;
    background: #041832;
    text-align: center;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.5);
    white-space: pre-wrap;
    padding: 8px 30px;
  }
}
</style>

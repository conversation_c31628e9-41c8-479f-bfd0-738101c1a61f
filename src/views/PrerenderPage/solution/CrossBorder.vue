<template>
  <div class="industry-wrap">
    <div class="header" style="background: #85581f">
      <img
        class="header-img"
        src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/banner.png"
        alt=""
      />
      <div class="title">跨境行业线</div>
      <div class="desc">引领合规的跨境支付平台，通过支付链接中国与世界。</div>
      <a
        class="btn"
        href="https://www.yeepay.com/customerService/businessCooperation"
        target="_blank"
        >立即咨询 ></a
      >
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="text-wrap">
          <div class="title">跨境收款方案</div>
          <div class="title-desc">
            为从事出口业务的客户提供全流程的跨境收款解决方案。
          </div>
          <a
            class="link-btn"
            href="https://open.yeepay.com/docs/v2/products/cbp/index.html"
            >了解更多 ></a
          >
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">跨境收结汇</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                接收境外外币或离岸人民币，转为在岸人民币
              </p>
            </li>
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">境内分发</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                通过客户的分发请求，对已完成收结汇的资金进行境内的分发或供应商打款
              </p>
            </li>
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">牌价查询</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                提供可参考的实时牌价，客户可根据参考牌价进行收结汇操作
              </p>
            </li>
          </ul>
        </div>
        <div class="img-wrap">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/shoukuan.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="img-wrap" style="margin-right: 100px">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/fukuan.png"
            alt=""
          />
        </div>
        <div class="text-wrap">
          <div class="title">跨境付款解决方案</div>
          <div class="title-desc">
            为进口电商为主的类似客户提供全流程的跨境付款解决方案
          </div>
          <a
            class="link-btn"
            href="https://open.yeepay.com/docs/v2/products/cbp/index.html"
            >了解更多 ></a
          >
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">海关推单</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                易宝将支付订单信息和平台订单推送至海关，与物流公司推送的物流单完成匹配
              </p>
            </li>
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">网关购汇</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                易宝为客户提供境内收单-海关推单-购付汇的全流程服务
              </p>
            </li>
            <li class="project-item">
              <p style="color: rgba(0, 0, 0, 0.85)">跨境汇款</p>
              <p style="color: rgba(0, 0, 0, 0.65)">
                根据客户的购付汇请求，将资金打款至境外的银行账户
              </p>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="content">
      <Industry :sceneList="sceneList" />
      <h2 class="h2">行业优势</h2>
      <ul class="advantage-list">
        <li class="advantage-item">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/advantage1.png"
            alt=""
            class="advantage-img"
          />
          <p class="advantage-title">引领合规</p>
          <p class="advantage-desc">为商户的资金流提供安全合规的保障</p>
        </li>
        <li class="advantage-item">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/advantage2.png"
            alt=""
            class="advantage-img"
          />
          <p class="advantage-title">深耕行业</p>
          <p class="advantage-desc">切合商户的实际需求提供定制化的服务</p>
        </li>
        <li class="advantage-item">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/advantage3.png"
            alt=""
            class="advantage-img"
          />
          <p class="advantage-title">安全高效</p>
          <p class="advantage-desc">强大的系统能力为资金护航，高到账时效性。</p>
        </li>
      </ul>

      <div class="license-wrap">
        <h2 class="h2">跨境牌照</h2>
        <ul class="license-list">
          <li class="license-item">
            <img
              src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/license1.png"
              alt=""
            />
          </li>
          <li class="license-item">
            <img
              src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/license2.png"
              alt=""
            />
          </li>
          <li class="license-item">
            <img
              src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/license3.png"
              alt=""
            />
          </li>
          <li class="license-item">
            <img
              src="https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/license4.png"
              alt=""
            />
          </li>
        </ul>
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
export default {
  data() {
    return {
      sceneList: [
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene1.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene1-2.png' +
            ')',
          closeText: '跨境出口电商收结汇',
          product: '产品功能: 跨境收汇，跨境结汇，境内下发，供应商付款',
          productUrl: 'https://open.yeepay.com/docs/v2/products/cbp/index.html',
          target: '_self',
          openText: [
            '为跨境出口电商收结汇提供安全·高效·定制化的资金流解决方案。',
          ],
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene2-2.png' +
            ')',
          closeText: '跨境进口电商购付汇',
          product: '产品功能：网关购汇，跨境汇款，海关推单',
          productUrl: 'https://open.yeepay.com/docs/v2/products/cbp/index.html',
          target: '_self',
          openText: ['为跨境进口电商提供合规·便捷·高效的购付汇资金流。'],
        },
      ],
    }
  },
}
</script>
<style lang="less">
.h2 {
  text-align: center;
  margin-bottom: 60px;
}
.advantage-list {
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: 186px;
  text-align: center;
  .advantage-item {
    display: inline-block;
    vertical-align: top;
    margin-right: 57px;
    &:last-child {
      margin-right: 0;
    }
    .advantage-img {
      height: 120px;
      margin-bottom: 19px;
    }
    .advantage-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 8px;
    }
    .advantage-desc {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 20px;
      margin-bottom: 8px;
    }
  }
}
.license-wrap {
  margin: 0 auto;
  width: 1280px;
  height: 586px;
  background: url('https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/world_vector.png')
    no-repeat center;
  background-size: 100% 100%;
  .license-list {
    text-align: center;
    .license-item {
      height: 338px;
      width: 282px;
      margin-right: 24px;
      display: inline-block;
      vertical-align: top;
      &:last-child {
        margin-right: 0;
      }
      img {
        height: 338px;
        width: 282px;
      }
    }
  }
}
</style>

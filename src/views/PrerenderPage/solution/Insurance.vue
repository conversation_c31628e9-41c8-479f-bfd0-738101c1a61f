<template>
  <div class="industry-wrap">
    <div class="header" style="background: #001015">
      <img
        class="header-img"
        src="https://img.yeepay.com/fe-resources/yop-docs/images/insurance/banner.png"
        alt=""
      />
      <div class="title">保险行业</div>
      <div class="desc">
        2011年成立以来，专注于行业客户资金的归结、结算、管理，主力发展保险电子商户，优化保险业渠道结构，培训新的业务增长点。
      </div>
      <a
        class="btn"
        href="https://www.yeepay.com/customerService/businessCooperation"
        target="_blank"
        >立即咨询 ></a
      >
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="text-wrap" style="margin-right: 78px">
          <div class="title">易码付行业方案</div>
          <div class="title-desc">
            以二维码支付为切入口，为商户提供综合资金服务平台，解决线上、线下多种业务场景并存的情况。
            密界面，根据商户需求和场景灵活应用
          </div>
          <a
            class="link-btn"
            href="https://open.yeepay.com/docs/v2/products/ymf/index.html"
            >了解更多 ></a
          >
        </div>
        <div class="img-wrap">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/insurance/yimafu.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="content">
      <Industry :sceneList="sceneList" />
      <div class="partner-wrap">
        <h2>合作伙伴</h2>
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/insurance/partner.png"
          alt=""
        />
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
export default {
  data() {
    return {
      sceneList: [
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene1.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene1-2.png' +
            ')',
          closeText: '线下面对面',
          product: '相关产品: 易码付',
          productUrl: 'https://open.yeepay.com/docs/v2/products/ymf/index.html',
          target: '_self',
          openText: {
            content1: '支持面对面台牌、嵌入智POS支付',
          },
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene2-2.png' +
            ')',
          closeText: '线上移动端',
          product: '相关产品: 易码付',
          productUrl: 'https://open.yeepay.com/docs/v2/products/ymf/index.html',
          target: '_self',
          openText: {
            content1: '支持线上公众号、移动端APP、小程序API',
          },
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene3.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/insurance/scene3-2.png' +
            ')',
          closeText: '线上PC端',
          product: '相关产品: 易码付',
          productUrl: 'https://open.yeepay.com/docs/v2/products/ymf/index.html',
          target: '_self',
          openText: {
            content1:
              '嵌入商户PC网站/系统，商户根据易码付规则生成二维码，用户扫码后选择支付方式，核对支付金额进行支付',
          },
        },
      ],
    }
  },
}
</script>

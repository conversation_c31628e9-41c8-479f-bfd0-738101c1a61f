<template>
  <div class="guide-wrap">
    <template v-if="html || content">
      <FaqDoc v-if="templateId === 'DOC_FAQS'" :faqList="docFaqList" />
      <a-row v-else>
        <a-col :md="24" :lg="comAnchorList.length > 1 ? 20 : 24">
          <RenderMarkdown :html="html" @getAnchorList="getAnchorList" />
           <FapPage :faqList="pageFaqList" />
        </a-col>
        <a-col
          :md="0"
          :lg="comAnchorList.length > 1 ? 4 : 0"
          style="padding-left: 20px"
          v-if="!isMobile"
        >
          <AnchorList :anchorList="comAnchorList" />
        </a-col>
      </a-row>
    </template>

    <div v-else-if="!loading" style="padding-top: 200px">
      <LazyComponent :time="500">
        <a-empty />
      </LazyComponent>
    </div>
  </div>
</template>
<script>
import RenderMarkdown from '@/components/RenderMarkdown'
import FapPage from '@/components/FapPage'
import FaqDoc from '@/components/FaqDoc'
import utils from '@/utils'
export default {
  components: {
    RenderMarkdown,
    FaqDoc,
    FapPage
  },
  data() {
    return {
      guideDocs: '',
      anchorList: []
    }
  },
  computed: {
    html() {
      return this.$store.state.guide.html
    },
    content() {
      return this.$store.state.guide.content
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    loading() {
      return this.$store.state.loading
    },
    templateId() {
      return this.$store.state.guide.templateId
    },
    pageFaqList() {
      return this.$store.state.guide.pageFaqList
    },
    docFaqList() {
      return this.content ? this.content.items : []
    },
    comAnchorList() {
      const { anchorList,  pageFaqList } = this
      return utils.hanleAnchorList(anchorList, pageFaqList)
    }
  },
  methods: {
    getAnchorList(list) {
      this.anchorList = list
    }
  }
}
</script>

<style lang="less" scoped>
.guide-wrap {
  min-height: calc(100vh - 64px);
}
</style>

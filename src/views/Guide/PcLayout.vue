<template>
  <div class="simple-wrap">
    <a-layout id="components-layout" class="pc">
      <div
        class="trigger-wrap"
        @click="() => (collapsed = !collapsed)"
        :style="{
          background: '#fff',
          position: 'fixed',
          zIndex: 1000,
          bottom: '50px',
          left: !collapsed ? '231px' : 0
        }"
      >
        <a-icon
          class="trigger"
          :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        />
      </div>
      <a-layout style="margin-top: 2px">
        <div
          id="pc-left-menu"
          :style="{
            width: `${scrollBarWidth + 230}px`,
            top: `${top + 1}px`
          }"
        >
          <a-layout-sider
            width="230"
            :style="{
              height: '100%',
              background: '#fff'
            }"
            v-show="!collapsed"
            :trigger="null"
            collapsible
          >
            <h3 class="title">{{ $t('menu.guideTitle') }}</h3>
            <template v-if="menuList.length > 0">
              <a-menu
                :style="{
                  overflow: 'hidden',
                  borderRight: 0
                }"
                :inlineIndent="16"
                mode="inline"
                :default-selected-keys="[currentLocation]"
                :open-keys="openKeys"
                :selectedKeys="[currentLocation]"
                @openChange="onOpenChange"
                @click="onMenuClick"
              >
                <template v-for="(menu, menuIndex) in menuList">
                  <template v-if="menu.children && menu.children.length > 0">
                    <a-sub-menu
                      class="level-1"
                      :key="menu.location"
                      :id="`menu-${menuIndex}`"
                    >
                      <span slot="title">{{ menu.title }}</span>
                      <template v-for="subMenu in menu.children">
                        <a-menu-item
                          :id="subMenu.location"
                          :key="subMenu.location"
                          >{{ subMenu.title }}</a-menu-item
                        >
                      </template>
                    </a-sub-menu>
                  </template>
                  <template v-else>
                    <a-menu-item
                      class="level-1"
                      :id="menu.location"
                      :key="menu.location"
                      >{{ menu.title }}</a-menu-item
                    >
                  </template>
                </template>
              </a-menu>
            </template>
            <template v-else>
              <a-empty
                v-if="!loading"
                style="margin-top: 200px"
                :image="simpleImage"
              />
            </template>
          </a-layout-sider>
        </div>
        <a-layout
          :style="{
            padding: '16px',
            display: 'block',
            marginLeft: collapsed ? '0' : '232px'
          }"
        >
          <div
            :style="{
              width: `calc(100vw - ${(collapsed ? 0 : 262) +
                scrollBarWidth}px)`,
              background: '#fff',
              padding: '24px',
              paddingTop: 0
            }"
          >
            <Detail />
          </div>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>
<script>
import Detail from './Detail'
import { mapState } from 'vuex'
import { Empty } from 'ant-design-vue'
export default {
  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
  },
  components: {
    Detail
  },
  data() {
    return {
      collapsed: false,
      busy: false,
      openKeys: [this.$store.state.guide.upCurrentLocation]
    }
  },
  watch: {
    upCurrentLocation(newValue) {
      if (newValue) this.openKeys = [newValue]
    }
  },
  computed: {
    ...mapState('guide', ['menuList', 'currentLocation', 'upCurrentLocation']),
    top() {
      return this.$store.state.top
    },
    loading() {
      return this.$store.state.loading
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    }
  },
  methods: {
    onOpenChange(openKeys) {
      const length = openKeys.length
      if (!length) {
        this.openKeys = []
        return
      }
      this.openKeys = [openKeys[length - 1]]
      this.$store.commit('guide/setUpCurrentLocation', openKeys[length - 1])
    },
    onMenuClick({ keyPath }) {
      if (this.currentLocation === keyPath[0]) return
      this.$router.push(`/docs/platform/${keyPath[0]}`)
    }
  }
}
</script>
<style lang="less" scoped>
.pc-menu {
  .api-path {
    word-break: break-all;
    white-space: pre-line;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .api-name {
    word-break: break-all;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 17px;
  }
}
.search-menu {
  padding: 20px 15px 0;
}
</style>

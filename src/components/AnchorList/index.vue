<template>
  <a-anchor
    @click="handleClick"
    @change="onAnchorChange"
    :wrapperStyle="{
      paddingTop: '20px'
    }"
  >
    <template v-for="item in anchorList">
      <a-anchor-link
        v-if="item.children && item.children.length < 1"
        :href="item.id"
        :key="item.id"
        :title="item.title"
      />
      <a-anchor-link
        v-else
        :href="item.id"
        :key="item.id"
        :title="item.title"
        :class="{
          'api-anchor-wrap': item.children.find(
            item => item.id === currentAnchor
          )
        }"
      >
        <a-anchor-link
          v-for="item2 in item.children"
          :href="item2.id"
          :key="item2.id"
          :title="item2.title"
        />
      </a-anchor-link>
    </template>
  </a-anchor>
</template>

<script>
export default {
  props: ['anchorList'],
  data() {
    return {
      currentAnchor: ''
    }
  },
  methods: {
    onAnchorChange(currentAnchor) {
      this.currentAnchor = currentAnchor
    },
    handleClick(e) {
      e.preventDefault()
    }
  }
}
</script>

<style></style>

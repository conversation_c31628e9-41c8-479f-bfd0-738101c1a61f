<template>
	<div class="code-wrap">
		<div class="select-header">
			<div class="lang">
				<div class="label">{{$t('codeLang')}}：</div>
				<a-radio-group v-model="value">
					<template v-for="item in Object.keys(definition.sampleCodes)">
						<a-radio-button :value="item" :key="item">{{ item === "CSHARP" ? "C#" : item }}</a-radio-button>
					</template>
				</a-radio-group>
			</div>
      <div class="code-link">
        <a :href="currentCodeLink.link" target="_blank">{{currentCodeLink.label}}</a>
      </div>
		</div>
		<Code
			v-if="definition.sampleCodes"
			:code="definition.sampleCodes[value]"
			:lang="value"
			:key="`${value}+${new Date()}`"
		/>
	</div>
</template>

<script>
import Code from '@/components/Code'
export default {
  components: {
    Code
  },
  data() {
    return {
      value: 'JAVA',
      codeLink: {
        JAVA: {
          label: '查看JAVA-SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/platform/sdk_guide/java-sdk-guide#2-%E5%BC%95%E5%85%A5-%E4%B8%9A%E5%8A%A1SDK-'
        },
        CSHARP: {
          label: '下载C#-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
        PHP: {
          label: '查看PHP-SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/platform/sdk_guide/php-sdk-guide'
        },
        PYTHON: {
          label: '下载PYTHON-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
      }
    }
  },
  computed: {
    definition() {
      return (this.$store.state.apiDocs.definition && this.$store.state.apiDocs.definition.apiId) ? this.$store.state.apiDocs.definition : this.$store.state.solutions.definition
    },
    currentCodeLink() {
      return this.codeLink[this.value]
    }
  },
  methods: {
    copy() {
      var Url2 = this.$refs[this.value].querySelector('code').innerText
      var oInput = document.createElement('textarea') //创建一个隐藏input
      oInput.value = Url2 //赋值
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.className = 'oInput'
      oInput.style.display = 'none'
      this.$message.success('复制成功')
    }
  }
}
</script>
<style lang="less" scoped>
	.code-wrap {
    .select-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 0;
      .lang {
        flex-shrink: 0;
        margin-bottom: 12px;
      }
      .code-link {
        margin-bottom: 12px;
      }
    }
		.code-content {
			position: relative;
			cursor: pointer;
			.copy-btn {
				position: absolute;
				right: 20px;
				top: 5px;
				z-index: 100;
				display: none;
			}
			&:hover {
				.copy-btn {
					display: block;
				}
			}
			.pre {
				height: 460px;
				overflow-y: auto;
			}
		}
	}
</style>

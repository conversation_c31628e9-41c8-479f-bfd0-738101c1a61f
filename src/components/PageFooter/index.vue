<template>
  <div class="footer">
    <div class="footer-link">
      <div class="left">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="phone">{{ $t('serviceHotline') }}</div>
        <div class="phone-num">
          {{ $t('phonenum') }}
          <span class="sub-des">{{ $t('phonenumsub') }} </span>
          <div class="img-feishu-weixin">
            <div class="mask">
              <img
                src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
                alt=""
              />
            </div>
            <img
              src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
              alt=""
            />
          </div>
        </div>
        <ul class="zizhi-wrap">
          <li
            class="zizhi-item"
            v-for="(item, index) in zizhiList"
            :key="index"
          >
            <a :href="item" :class="`bg${index + 1}`" target="_blank"></a>
          </li>
        </ul>
      </div>
      <div class="right">
        <ul class="link-list" v-for="item in footerLinkList" :key="item.title">
          <li class="link-title">{{ $t(item.title) }}</li>
          <li
            class="link-item"
            v-for="subItem in item.children"
            :key="subItem.title"
          >
            <a class="link-text" :href="subItem.contentUri">{{
              $t(subItem.title)
            }}</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="footer-wrap">
      {{ $t('footerText', { time: new Date().getFullYear() }) }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      zizhiList: [
        'https://www.yeepay.com/paymentLicense',
        'https://www.yeepay.com/businessLicense',
        'javascript:void(0)',
        'https://www.yeepay.com/yeepaypci',
        'http://www.cyberpolice.cn/wfjb/',
        'https://ss.knet.cn/verifyseal.dll?sn=2011051700100008785&ct=df&a=1&pa=0.761116063861185',
        'javascript:void(0)',
      ],
    }
  },
  computed: {
    footerLinkList() {
      return this.$store.state.footerLinkList
    },
  },
}
</script>

<style lang="less" scoped>
.footer-link {
  width: 1280px;
  margin: 0 auto;
  height: 360px;
  padding: 50px 16px 0 10px;
  .left {
    display: inline-block;
    vertical-align: top;
    margin-right: 100px;
    text-align: left;
    .img {
      display: block;
      height: 48px;
      width: 164px;
      margin-bottom: 86px;
    }
    .phone {
      height: 16px;
      font-size: 11px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #000000;
      line-height: 16px;
      margin-bottom: 8px;
    }
    .phone-num {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
      margin-bottom: 57px;
      .sub-des {
        width: 84px;
        height: 13px;
        font-size: 10px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #000000;
        line-height: 13px;
      }
      .img-feishu-weixin {
        display: inline-block;
        width: 32px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #efefef;
        margin-top: -10px;
        position: relative;
        cursor: pointer;
        &:hover {
          .mask {
            display: block;
          }
        }
        .mask {
          position: absolute;
          left: -44px;
          bottom: 40px;
          width: 120px;
          height: 120px;
          display: none;
        }
        img {
          width: 100%;
        }
      }
    }
    .zizhi-wrap {
      .zizhi-item {
        display: inline-block;
        margin-right: 4px;
        a {
          display: inline-block;
          height: 21px;
          background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
            no-repeat;
          background-size: 248px 21px;
        }
        .bg1 {
          width: 25px;
        }
        .bg2 {
          width: 30px;
          background-position-x: -29px;
        }
        .bg3 {
          width: 30px;
          background-position-x: -62px;
        }
        .bg4 {
          width: 25px;
          background-position-x: -100px;
        }
        .bg5 {
          width: 20px;
          background-position-x: -128px;
        }
        .bg6 {
          width: 41px;
          background-position-x: -150px;
        }
        .bg7 {
          width: 55px;
          background-position-x: -194px;
        }
      }
    }
    .zizhi {
      height: 21px;
    }
  }
  .right {
    float: right;
    .link-list {
      text-align: left;
      display: inline-block;
      vertical-align: top;
      margin-right: 80px;
      &:last-child {
        margin-right: 0;
      }
      .link-title {
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #000000;
        line-height: 22px;
        margin-bottom: 16px;
      }
      .link-item {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: #000000;
        line-height: 20px;
        margin-bottom: 8px;
        a {
          color: #000000;
        }
      }
    }
  }
}
.footer-wrap {
  width: 100%;
  height: 32px;
  background: #041832;
  text-align: center;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serif;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.5);
  line-height: 32px;
}
</style>

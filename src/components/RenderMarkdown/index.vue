<template>
	<div class="renderMarkdown-wrap">
		<div class="content code-wrap" ref="markdown" id="markdown"></div>
		<div v-if="showEmpty" style="padding-top: 200px">
			<a-empty />
		</div>
		<div class="images" v-show="false" v-viewer="options"></div>
	</div>
</template>

<script>
import Vditor from 'vditor/dist/method.min.js'
import 'vditor/dist/index.css'
import { Modal } from 'ant-design-vue'
import Api from '@/api'
export default {
  props: ['html'],
  data() {
    return {
      docs: '',
      showEmpty: false,
      anchorId: '',
      options: {
        navbar: false,
        button: false,
        title: false,
        toolbar: true,
        tooltip: false,
        movable: false,
        zoomable: true,
        rotatable: false,
        scalable: false,
        fullscreen: false,
        keyboard: false,
      },
    }
  },
  watch: {
    html(newValue) {
      this.renderHtml(newValue)
    },
  },
  created() {
    this.anchorId = decodeURIComponent(this.$route.hash)
  },
  mounted() {
    const that = this
    window.goToUrl = function (url) {
      Api.checkLink(encodeURIComponent(url)).then((res) => {
        switch (res.data.type) {
          case 'DIRECT':
            if(that.isIOS()) {
              window.location.href = res.data.targetLink
            } else {
              window.open(res.data.targetLink)
            }
            break
          case 'ALERT':
            Modal.confirm({
              title: '确认继续访问？',
              closable: true,
              icon: 'info-circle',
              content: (h) => {
                return h('div', [
                  h('p', '继续访问将跳转到外部网站，其安全性未知。'),
                  h(
                    'a',
                    {
                      style: {
                        cursor: 'default',
                        overflow: 'hidden',
                        wordBreak: 'break-all',
                        textOverflow: 'ellipsis',
                        borderRadius: '5px',
                        height: '40px',
                        width: '100%',
                        padding: '0 10px',
                        lineHeight: '40px',
                        whiteSpace: 'nowrap',
                        display: 'inline-block',
                        background: 'rgb(239,239,239)',
                      },
                    },
                    url
                  ),
                ])
              },
              okText: '继续访问',
              cancelText: '取消',
              onOk: () => {
                window.open(res.data.targetLink)
              },
            })
            break
          case 'FORBID':
            break
          default:
            break
        }
      })
    }
    this.renderHtml(this.html)
    this.previewElement = this.$refs.markdown
    this.previewElement.addEventListener('click', function (event) {
      if (event.target.tagName === 'IMG') {
        that.$el.querySelector('.images').innerHTML = ''
        const node = event.target.cloneNode(true)
        that.$el.querySelector('.images').appendChild(node)
        const viewer = that.$el.querySelector('.images').$viewer
        viewer.show()
      }
    })
  },
  methods: {
    isIOS() {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) &&  !window.MSStream
    },
    handleAnchorList(html) {
      const contentList = html.querySelectorAll('.markdown-con')
      const listMap = {}
      let currentH2 = ''
      contentList.forEach((content) => {
        content.childNodes.forEach((element) => {
          if (element.nodeName === 'H2') {
            listMap[element.id] = {
              id: '#' + element.id,
              title: element.innerHTML,
              children: [],
            }
            currentH2 = element.id
          }
          if (element.nodeName === 'H3' && currentH2) {
            listMap[currentH2].children.push({
              id: '#' + element.id,
              title: element.innerHTML,
            })
          }
        })
      })
      return Object.values(listMap)
    },
    renderHtml(value) {
      if (Array.isArray(value)) {
        this.showEmpty = true
      }
      const markdownDom = this.$refs.markdown
      if (!markdownDom) return
      if (!value || Array.isArray(value)) {
        this.docs = ''
        this.$refs.markdown.innerHTML = ''
        return
      }
      this.showEmpty = false
      markdownDom.style.visibility = 'hidden'
      markdownDom.innerHTML = value
      const list = markdownDom.querySelectorAll('.markdown-con')
      let proListArr = []
      for (let i = 0; i < list.length; i++) {
        const item = new Promise((resolve) => {
          Vditor.preview(
            list[i],
            list[i].innerHTML
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&quot;/g, '\'')
              .replace(/&#39;/g, '\'')
              .replace(/<\/string,string><\/string,string>/g, '')
              .replace(/<\/string,>/g, '')
              .replace(/<\/string,string>/g, ''),
            {
              className: 'preview vditor-reset',
              cdn: 'https://img.yeepay.com/yop_doc/docs/static',
              theme: {
                path: 'https://img.yeepay.com/yop_doc/docs/static/dist/css/content-theme',
              },
              hljs: {
                enable: true,
                lineNumber: true,
              },
              anchor: 0,
              speech: {
                enable: false,
              },
              after: () => {
                resolve()
              },
            }
          )
        })
        proListArr.push(item)
      }
      Promise.all(proListArr).then(() => {
        const list = this.handleAnchorList(markdownDom)
        this.$emit('getAnchorList', list)
        setTimeout(() => {
          markdownDom.style.visibility = 'visible'
          this.jumpToAnchor()
        }, 200)
      })
    },
    jumpToAnchor() {
      if (!this.anchorId) return
      const anchorDom = document.getElementById(this.anchorId.replace(/^#/, ''))
      if (anchorDom) {
        anchorDom.scrollIntoView()
        this.anchorId = ''
      }
    },
  },
}
</script>
<style lang="less">
	.viewer-canvas > img {
		background-color: #fff !important;
		padding: 15px !important;
	}
	.renderMarkdown-wrap {
		border-top: 1px solid transparent; // 防止外边距重叠
		.messageText {
			stroke: none !important;
		}
		.messageLine0 {
			stroke-width: 1 !important;
		}
		.vditor-linenumber__rows {
			background: #f7f7f7;
			top: 0;
			bottom: 0;
			padding-top: 12px;
			span {
				&:before {
					color: #999;
				}
			}
		}
		#markdown {
			img {
				max-width: 95%;
				margin: 10px 0;
				display: block;
			}
			.language-mermaid {
				background: #fff;
			}
			[id^='mermaid-'] {
				.messageLine0 {
					stroke-width: 1 !important;
				}
				.actor-line {
					stroke: #000;
				}
				.messageText {
					font-size: 12px !important;
					fill: #666;
				}
				rect {
					stroke: #000;
					fill: #fff;
				}
				.note {
					stroke: none;
					fill: #eff7fe;
				}
				.noteText {
					fill: rgba(0, 0, 0, 0.65);
				}
				.loopLine {
					stroke: #52bf63;
					stroke-width: 1;
					stroke-dasharray: 2 2;
				}
				.labelBox {
					fill: #52bf63;
					opacity: 0.1;
				}
				.labelText {
					fill: #52bf63;
				}
			}
		}
		.vditor-reset {
			font-size: 14px;
			h1 {
				border-bottom: 1px solid rgba(232, 232, 232, 1) !important;
				margin-top: 0;
			}
			li {
				list-style: initial;
			}
			ol {
				li {
					list-style-type: decimal;
				}
			}
		}
		table {
			overflow-x: auto;
			width: 100%;
			border-collapse: collapse;
			empty-cells: show;
			margin-bottom: 16px;
			display: block;
			border-spacing: 0;
			tr {
				background-color: #fff;
				/* border-top:1px solid #c6cbd1 */
				td,
				th {
					border: 1px solid #e8eaec;
				}
			}
			td,
			th {
				min-width: 72px;
				padding: 4px 12px;
				word-break: break-word;
			}
			th {
				background-color: #f8f8f9;
				min-width: 72px;
				/* font-weight:600 */
			}
		}
	}
</style>

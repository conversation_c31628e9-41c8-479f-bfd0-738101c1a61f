<template>
  <a-sub-menu
    :key="menuInfo.location"
    v-bind="$props"
    v-on="$listeners"
    :class="`level-${level}`"
  >
    <span slot="title">
      <span>{{ menuInfo.name }}</span>
    </span>
    <template
      v-if="
        menuInfo.children &&
          menuInfo.children.length === 1 &&
          menuInfo.children[0].code === 'others'
      "
    >
      <template v-for="menu in menuInfo.children[0].items">
        <template v-if="menu.show">
          <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
            menu.title
          }}</a-menu-item>
        </template>
        <template v-else-if="!menu.latestRef || menu.latestRef === menu.apiId">
          <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
            menu.title
          }}</a-menu-item>
        </template>
      </template>
    </template>
    <template v-else>
      <template v-if="menuInfo.children && menuInfo.children.length > 0">
        <template v-for="menu in menuInfo.children">
          <ApiMenuItem
            :level="level + 1"
            :menu-info="menu"
            :key="menu.location"
            v-bind="$props"
            v-on="$listeners"
          />
        </template>
      </template>
      <template v-else>
        <template v-for="menu in menuInfo.items">
          <template v-if="menu.show">
            <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
              menu.title
            }}</a-menu-item>
          </template>
          <template
            v-else-if="!menu.latestRef || menu.latestRef === menu.apiId"
          >
            <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
              menu.title
            }}</a-menu-item>
          </template>
        </template>
      </template>
    </template>
  </a-sub-menu>
</template>

<script>
import { Menu } from 'ant-design-vue'
export default {
  name: 'ApiMenuItem',
  isSubMenu: true,
  props: {
    ...Menu.SubMenu.props,
    // Cannot overlap with properties within Menu.SubMenu.props
    menuInfo: {
      type: Object,
      default: () => ({})
    },
    level: {
      type: Number,
      default: 1
    }
  }
}
</script>

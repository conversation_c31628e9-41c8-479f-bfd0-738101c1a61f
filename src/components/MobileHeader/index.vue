<template>
  <div
    class="mobile-nav"
    :style="{
      boxShadow: mobileLeftShow
        ? 'none'
        : '0px 1px 4px 0px rgba(0, 21, 41, 0.12)',
    }"
  >
    <a class="logo" href="/mhome">
      <div class="img"></div>
    </a>
    <div class="right-icon">
      <a-icon
        type="menu"
        v-if="!mobileLeftShow"
        @click="showMenu"
        style="color: #000; font-size: 16px"
      />
      <a-icon
        type="close"
        v-else
        @click="showMenu"
        style="color: #000; font-size: 16px"
      />
    </div>
    <div
      v-if="!mobileLeftShow && $scopedSlots.$hasNormal"
      class="sub-mobile-icon"
      @click="showSubMenu"
      :style="{
        left: subMenu ? '300px' : 0,
        boxShadow: !subMenu ? '0px 2px 10px 0px rgba(0, 0, 0, 0.11)' : 'none',
      }"
    >
      <img v-if="!subMenu" src="@/assets/images/icon-list.png" alt />
      <a-icon v-else type="close" style="font-size: 14px" />
    </div>
    <template v-if="$scopedSlots.$hasNormal">
      <div
        slot="overlay"
        class="mobile-top-menu"
        @click.self="showSubMenu"
        v-if="subMenu"
      >
        <div class="submenu">
          <slot></slot>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import header from '@/mixins/header'
export default {
  mixins: [header],
  props: ['navName'],
  data() {
    return {}
  },
  computed: {
    mobileLeftShow() {
      return this.$store.state.mobileLeftShow
    },
    scrollTop() {
      return this.$store.state.scrollTop
    },
    subMenu: {
      get() {
        return this.$store.state.subMenu
      },
      set(value) {
        this.$store.commit('setSubMenu', value)
      },
    },
  },
  watch: {
    subMenu(newValue) {
      this.$store.dispatch('toggleLock', newValue)
    },
    mobileLeftShow: {
      handler(newValue) {
        this.$store.dispatch('toggleLock', newValue)
      },
      immediate: true,
    },
  },
  methods: {
    goHome() {},
    showSubMenu() {
      this.subMenu = !this.subMenu
      this.$store.commit('setMobileLeftShow', false)
    },
    showMenu() {
      this.subMenu = false
      this.$store.commit('toggleMobileLeftShow')
    },
  },
}
</script>

<style lang="less" scoped>
.mobile-top-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9;
  .submenu {
    width: 300px;
  }
}
.sub-mobile-icon {
  position: fixed;
  top: 90px;
  left: 0;
  background: #fff;
  z-index: 10;
  width: 32px;
  height: 32px;
  border-radius: 0px 100px 100px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 14px;
    height: 14px;
  }
}
.mobile-nav {
  justify-content: flex-start !important;
  padding-left: 20px;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12) !important;
  .logo {
    display: inline-block;
    line-height: 64px;
    cursor: pointer;
    margin-right: 59px;
    .img {
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: 201px 32px;
    }
  }
  .right-icon {
    position: absolute;
    top: 13px;
    right: 15px;
    .change-lang {
      width: 24px;
      height: 24px;
      line-height: 24px;
      display: inline-block;
      vertical-align: middle;
      cursor: pointer;
      font-size: 14px;
      fill: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>

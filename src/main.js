import 'core-js/stable'
import 'regenerator-runtime/runtime'
import 'babel-polyfill'
import './utils/base64'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import i18n from './i18n'
import store from './store'
import directive from './utils/directive'
import Viewer from 'v-viewer'
import { upLog } from '@/api/request'
import { Tooltip } from 'view-design'
import components from '@/components'
import renderer, { injectRequest }  from '@yeepay/lowcode-renderer'
import '@yeepay/lowcode-renderer/dist/styles/index.less'
// 样式文件
import 'viewerjs/dist/viewer.css'
import './styles/my-theme.less'
import './utils/importUI'
import './styles/global.less'

// fundebug
import * as fundebug from 'fundebug-javascript'
import fundebugVue from 'fundebug-vue'
import axios from 'axios'
fundebug.init({
  apikey: 'a4eaa393d11a7cb850885db23565ec228464345ddb476ef8b1ae5504560ddc1b',
  silentDev: true,
  sampleRate: 0.3,
  filters : [
    {
      req: {
        method: /^GET$/
      },
      res: {
        status: /^403$/
      }
    }
  ]
})
fundebugVue(fundebug, Vue)

Vue.config.productionTip = false
Vue.prototype.$upLog = upLog
Vue.use(Viewer)
Vue.use(directive)
Vue.use(components)
Vue.use(renderer)
injectRequest(axios)
Vue.component('Tooltip', Tooltip)
new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
  mounted() {
    // You'll need this for renderAfterDocumentEvent.
    document.dispatchEvent(new Event('render-event'))
  }
}).$mount('#app')

import message from 'ant-design-vue/es/message'
import router from '@/router'
import axios from 'axios'
import handleUrl from './lang'

const instance = axios.create({
  baseURL: '/apis',
  validateStatus: function (status) {
    // 401, 403 在拦截器中 resolve 处理, 由于可能需要其报文
    return (
      (status >= 200 && status < 300) ||
      status === 401 ||
      status === 403 ||
      status === 404
    )
  },
})
// 添加请求拦截器
instance.interceptors.request.use(
  function (config) {
    // 在发送请求之前做些什么
    return config
  },
  function (error) {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  function (response) {
    // IE 8-9
    if (
      response.data == null &&
      response.config.responseType === 'json' &&
      response.request.responseText != null
    ) {
      try {
        // eslint-disable-next-line no-param-reassign
        response.data = JSON.parse(response.request.responseText)
      } catch (e) {
        // ignored
      }
    }
    if (response.status && response.status === 403) {
      return response
    }
    if (response.status && response.status === 404) {
      router.push('/docs/404')
      return []
    }
    // 对响应数据做点什么
    return response.data
  },
  function (error) {
    // 对响应错误做点什么
    if (error.message) message.error(error.message)
    return Promise.reject(error)
  }
)
export async function get(url) {
  const langType = window.localStorage.getItem('langType')
  const filterUrl = handleUrl(url)
  const res = await instance({
    url: filterUrl,
    method: 'get',
  })
  if (res.status && res.status === 403) {
    if (langType === 'en_US') {
      const resCn = await instance({
        url: url,
        method: 'get',
      })
      if (resCn.status && resCn.status === 403) {
        return []
      }
      return resCn
    }
    return []
  }
  return res
}
export function post(url, params) {
  return instance({
    url,
    data: params,
    method: 'post',
  })
}
// const upLogInstance = axios.create({
//   baseURL: 'https://qaprobe.yeepay.com'
// })
export function upLog(id) {
  console.log(id)
  // const params = {
  //   userId: '12345',
  //   locationPointId: id
  // }
  // return upLogInstance({
  //   url: '/server/upBp?' + qs.stringify(params, { indices: false }),
  //   method: 'get'
  // })
}
export default instance

import { get } from './request'

export default {
  checkLink(url) {
    return get(`/commons/go?url=${url}`)
  },
  getMenuTree() {
    return get('docs/apis/tree.json')
  },
  getApiExtend(params) {
    return get(`docs/products/${params.docNo}/api_extend/${params.pageNo}.json`)
  },
  getCommon(params) {
    const url = `docs/apis/common/${params.refName}.json`
    return get(url)
  },
  getDefinition(params) {
    const url = `docs/apis/${params.apiId}/definition.json`
    return get(url)
  },
  getHistory(params) {
    const url = `docs/apis/${params.apiId}/history.json`
    return get(url)
  },
  getErrcode(params) {
    const url = `docs/apis/${params.apiId}/errcode.json`
    return get(url)
  },
  getSpi(params) {
    const url = `docs/apis/${params.apiId}/spi.json`
    return get(url)
  },
  getModels(params) {
    const url = `docs/models/${params.apiGroupCode}/${params.modelName}.json`
    return get(url)
  },
  getCallbackSpis(params) {
    const url = `docs/spis/${params.spiName}.json`
    return get(url)
  },
  // 获取spi api 关联数据
  getSpiApiMap(params) {
    const url = `docs/apis/${params.apiId}/spi-api-mapping.json`
    return get(url)
  },
  // 获取api 基本信息 
  getApibasic() {
    const url = `docs/apis/basic.json`
    return get(url)
  }
}

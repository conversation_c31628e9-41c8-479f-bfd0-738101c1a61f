export default {
  tongzhicanshu: 'Notification Parameters',
  shilibaowen: 'Sample Message',
  copy: 'Copy',
  yes: 'Yes',
  no: 'No',
  recommend: 'Recommend',
  unnecessary: 'Unnecessary',
  copySuccess: 'Copy succeeded',
  serach: 'Search',
  filter: 'Filter',
  all: 'All',
  searchNoResult: 'Sorry, no results',
  update: 'Updated date：',
  tableParamsTip:
    'This object({name}) is the same object as layer {level} ({name}) and will not be displayed again',
  searchTip:
    '“<span style="color: #52bf63">{searchTipWord}</span>”And the words that follow it are ignored,Because the query keyword is limited to <span style="color: #52bf63">40</span> characters.',
  reset: 'Reset',
  pleaseInput: 'Please enter the',
  apiOverview: 'An overview of the API',
  codeLang: 'Lang',
  langTitle: '中文',
  keyWordSearch: 'Search',
  loginDeveloper: 'Sign in',
  loginDeveloperM: 'Sign in',
  kuajingLogin: 'International Business',
  biaozhunLogin: 'Domestic Business',
  serviceHotline: '7 * 24 Service Hotline:',
  phonenum: '+86 95070',
  phonenumsub: '(YOP-7)',
  footerText: '©2003-{time} YeePay Co., Ltd. All Rights Reserved.',
  home: {
    title: 'Open platform',
    myService: 'My customer service',
    evaluate: 'Evaluation and Feedback',
    top: 'Back to the top',
  },
  updateTime: 'Update time',
  cfcaTip: 'need to use  CFCA certificate is required',
  midengTip: 'The result of making one or more requests is consistent',
  oauth2Tip: 'OAUTH2 authorization needs to be completed',
  securityTip: 'need to use {security} for signature verification',
  cfca: 'CFCA Certificate',
  mideng: 'Idempotent',
  sandbox: 'SandBox',
  oauth2: 'OAUTH2',
  security: 'Signature',
  menu: {
    home: 'Home',
    solution: 'Solutions',
    product: 'Products',
    doc: 'Documents',
    apiDocsTitle: 'API Documents',
    guideTitle: 'Guide',
    questionTitle: 'FAQ',
    developer: 'Developer',
    tyfa: 'General Plan',
    bzshsfk: 'Standard Merchant',
    ptssfk: 'Platform Providers',
    fwssfk: 'Service Provider',
    hyfa: 'Industry solutions',
    retail: 'OnlineToOffline',
    bankAndFund: 'Bank And Fund',
    consumptionFinancial: 'Consumer finance',
    airTravel: 'AirTravel',
    administrative: 'Administrative',
    crossBorder: 'CrossBorder',
    electricity: 'Electricity',
    insurance: 'Insurance',
    zfcp: 'Payment products',
    yjzf: 'One click payment',
    eBank: 'E-currency payment ',
    wechatOffiaccount: 'WeChat official account payment',
    merchantScan: 'Business scan payment',
    userScan: 'User scan payment',
    miniProgram: 'Small program payment',
    sdkPay: 'UnionPay SDK payment',
    jsPay: 'UnionPay JS payment',
    alipayLife: 'Alipay life code payment',
    staticQR: 'Payment Card',
    zjgl: 'Fund management',
    recharge: 'Offline import',
    ptzz: 'Business to business transfer',
    dfdf: 'Payment and distribution',
    rzfw: 'Certification services',
    identify00001: 'Card Auth',
    identify00002: 'Id Auth',
    msgIdentify: 'Info Auth',
    userGuide: 'Quick Docking',
    certIntro: 'Key Configuration',
    sdkGuide: 'Docking API',
    notifySummary: 'Asynchronous Notification',
    apiAll: 'Overview API',
    developTools: 'Develop Tools',
    sdkTools: 'Key Tools',
    platformSdk: 'SDK',
    yopIsvGateway: 'Notification Tools',
    yizhanshi: 'Develop Plug-in',
    help: 'Help',
    callUs: 'Contact us',
    platformDesc: 'Platform Introduction',
    cooperation: 'Business Cooperation',
    yeepayCom: 'YeePay.com',
    deployTool: 'Deployment Tool',
    rongqiyun: 'Container Cloud Platform',
  },
  tableColumns: {
    title1: 'Parameters',
    title2: 'Type（Max-Length）',
    title3: 'Required',
    title4: 'Description',
    title5: 'Encrypt',
    tip: 'Example',
  },
  apiSearchTableColumns: {
    title1: 'Check API',
    title2: 'Description',
  },
  errorCodeTableColumns: {
    title1: 'SubErrorCode',
    title2: 'SubErrorCode Description',
    title3: 'Solution',
  },
  format: 'Format',
  securityReqsTip: 'Interface invokes security requirements',
  authorityTip:
    'This is a high risk interface and requires uploading the CFCA public key certificate in the Developer Center',
  historyMsg:
    'The current API is the historical version, we do not recommend docking according to the content of this page. For better technical support, you can click to view{action}。',
  typeTip:
    'The service may be used according to the comprehensive needs of the system and network but not limited to speed limit and concurrency limit, etc. Please use it reasonably according to the actual business scenario.',
  newVersion: 'The latest version',
  apiDesc: 'Interface description',
  instructions: 'Directions for use',
  commonRepParams: 'Public request parameter',
  catCommonRepParams: 'View public request parameters',
  commonRepParamsTip:
    'There is no SDK connection API, please check the request header parameters. If the SDK is used for interconnection, no concern is required.',
  repParams: 'Request Params',
  reqHeader: 'Request Header:',
  reqBody: 'Request Body',
  reqParameters: 'Query Params',
  catReqExamples: 'Request Sample',
  commonResParams: 'Common response parameter',
  commonResParamsTip:
    'There is no SDK interconnection API. Check the response header parameters. If the SDK is used for interconnection, no concern is required.',
  catCommonResParams: 'View the public response parameters',
  respParams: 'Response Params',
  resHeader: 'Response Header',
  resBody: 'Response Body',
  catRespExamples: 'Response Sample',
  catExm: 'See the sample',
  codeExm: 'Sample Code',
  errorCode: 'Business Error Codes',
  errorCodeTipA: 'Platform Errorcodes',
  errorCodeTip:
    'The business error code consists of the error code (fixed value: 40044, see {action} for details), the error description (fixed value: business processing failure), the sub-error code and the sub-error description, where the sub-error code, sub-error description and the corresponding solution are as follows:',
  callback: 'API Callbacks',
  callbackTip1A: 'Decryption method',
  callbackTip1:
    'Notification parameters: Response and customerIdentification. Response is a ciphertext string, which is decrypted into plaintext after being obtained.',
  callbackTip2A: 'More rules',
  callbackTip2:
    'Strategy: received yeepay correction notice need to write a capital "SUCCESS", if not write back to inform nine times, most retry delay time is respectively: 5,5,20,270,600,900,1800,3600,14400 (in seconds), 9 times didn\'t get back to writing it stop notice.{action}',
  callbackTip3: 'When you connect the result notification, be sure to access the checklist interface. If the notification timeout exceeds the tolerance of your system, you can call the order check interface to check the order status.',
  apiList: 'The API list',
  apiSearchOrder: 'Check API',
  apiListDes:
    'The following list contains all the interfaces involved in this product. Click "Interface Name" to see interface description, request parameters, request example, response parameters, response example, sample code, business error code.',
  apiName: 'The name of the interface',
  apply: 'Application scenarios',
  // # 报错页面
  '403ErrorTip': 'Sorry，you do not have access to the page.',
  '404ErrorTip': 'Sorry，The page you visited is not exist',
  '500ErrorTip': 'Sorry，The service is temporarily unavailable',
  getBackPage: 'Returns to the previous page',
  getHomePage: 'Return to the home page',
  reload: 'Reload',
  productName: 'The product name',
  moduleName: 'The name of the module',
  productModal: 'Product module',
  apiName2: 'The name of the API',
  desc: 'Describe',
  accessDiagnosis: 'Diagnosis',
}
